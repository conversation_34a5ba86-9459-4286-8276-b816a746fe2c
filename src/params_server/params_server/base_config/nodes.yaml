can_encoder:
  rate: 1
  make_remote_topics: true
  can_messages:
    carousel_ctrl:
      protocol: "x90CarouselCtrl"
      rate: 10
      channel: "/can_5/rx"
      fields:
        carousel: "carousel_swing"
        carousel_rot: "carousel_index"

    tower_pins_ctrl:
      protocol: "x90PinsCtrl"
      rate: 10
      channel: "/can_5/rx"
      fields:
        vert_pin: "tower_vert_pins"
        tilt_pin: "tower_incl_pins"

    tower_tilt_ctrl:
      protocol: "x90MastCtrl"
      rate: 10
      channel: "/can_5/rx"
      fields:
        mast: "tower_tilt"

    wrench_ctrl:
      protocol: "x90WrenchCtrl"
      rate: 10
      channel: "/can_5/rx"
      fields:
        wrench: "wrench_grip"
        wrench2: "wrench_swing"

    tracks_ctrl:
      protocol: "x90CatCtrl"
      rate: 10
      channel: "/can_5/rx"
      fields:
        cat_left: "tracks_left"
        cat_right: "tracks_right"

    arm_ctrl:
      protocol: "x90ArmCtrl"
      rate: 10
      channel: "/can_5/rx"
      fields:
        arm: "arm"

    fork_ctrl:
      protocol: "x90BracketCtrl"
      rate: 10
      channel: "/can_5/rx"
      fields:
        bracket: "fork"

    drill_ctrl:
      protocol: "x90RotSupCtrl"
      rate: 10
      channel: "/can_5/rx"
      fields:
        rot: "drill_rotation_speed"
        feed: "drill_feed_speed"

    drill_press_ctrl:
      protocol: "x90FanSupCtrl"
      rate: 10
      channel: "/can_5/rx"
      fields:
        rotation: "drill_torque_limit"
        feed: "drill_feed_pressure"

    air_ctrl:
      protocol: "compressor"
      rate: 10
      channel: "/can_2/rx"
      fields:
        compressor_relay: "air_enabled"
        compressor_power: "air_power"

    dust_flaps_ctrl:
      protocol: "x90DustFlapsCtrl"
      rate: 10
      channel: "/can_5/rx"
      fields:
        dust_flaps: "dust_flaps"

    jacks_front_ctrl:
      protocol: "x90JacksFrontCtrl"
      rate: 10
      channel: "/can_5/rx"
      fields:
        jack_left: "jacks_left"
        jack_right: "jacks_right"

    jacks_rear_ctrl:
      protocol: "x90JacksBackCtrl"
      rate: 10
      channel: "/can_5/rx"
      fields:
        jack_left: "jacks_rear"

    relay_ctrl:
      protocol: "x90HooverRelayCtrl"
      rate: 10
      channel: "/can_5/rx"
      fields:
        water_injection: "water"
        driller_relay: "drive_mode"

  values:
    carousel_swing:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 1000
      output_saturation_negative: -1000
      output_deadband_positive: 100
      output_deadband_negative: -100
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    carousel_index:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 1000
      output_saturation_negative: -1000
      output_deadband_positive: 100
      output_deadband_negative: -100
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    tower_vert_pins:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 32767
      output_saturation_negative: -32768
      safe_value: 0
      timeout: 0.5

    tower_incl_pins:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 32767
      output_saturation_negative: -32768
      safe_value: 0
      timeout: 0.5

    tower_tilt:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 1000
      output_saturation_negative: -1000
      output_deadband_positive: 1
      output_deadband_negative: -1
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    wrench_swing:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 350
      output_saturation_negative: -350
      output_deadband_positive: 277
      output_deadband_negative: -275
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    wrench_grip:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 32767
      output_saturation_negative: -32768
      safe_value: 0
      timeout: 0.5

    tracks_left:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 738
      output_saturation_negative: -678
      output_deadband_positive: 326
      output_deadband_negative: -344
      deadband_thr: 0.001
      polynomial_pos: [ 0.0, 1.76651, -3.34973, 2.58322 ]
      polynomial_neg: [ 0.0, 1.34466, -1.76995, 1.42529 ]
      safe_value: 0
      timeout: 0.5

    tracks_right:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 770
      output_saturation_negative: -735
      output_deadband_positive: 463
      output_deadband_negative: -404
      deadband_thr: 0.001
      polynomial_pos: [ 0.0, 1.32475, -1.82612, 1.50137 ]
      polynomial_neg: [ 0.0, 1.01008, -0.619589, 0.60951 ]
      safe_value: 0
      timeout: 0.5

    arm:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 300
      output_saturation_negative: -250
      output_deadband_positive: 235
      output_deadband_negative: -200
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    fork:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 1000
      output_saturation_negative: -1000
      output_deadband_positive: 100
      output_deadband_negative: -100
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    drill_feed_speed:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 629
      output_saturation_negative: -586
      output_deadband_positive: 331
      output_deadband_negative: -343
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    drill_rotation_speed:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 649
      output_saturation_negative: -668
      output_deadband_positive: 403
      output_deadband_negative: -407
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    drill_feed_pressure:
      input_max_positive: 1
      output_saturation_positive: 312
      output_deadband_positive: 1
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    drill_torque_limit:
      input_max_positive: 1
      output_saturation_positive: 880
      output_deadband_positive: 300
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    air_power:
      input_max_positive: 1
      output_saturation_positive: 100
      safe_value: 0
      timeout: 0.5

    air_enabled:
      safe_value: 0
      timeout: 0.5

    dust_flaps:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 32767
      output_saturation_negative: -32768
      output_deadband_positive: 100
      output_deadband_negative: -100
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    jacks_left:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 560
      output_saturation_negative: -500
      output_deadband_positive: 318
      output_deadband_negative: -318
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    jacks_right:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 500
      output_saturation_negative: -470
      output_deadband_positive: 313
      output_deadband_negative: -313
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    jacks_rear:
      input_max_positive: 1
      input_max_negative: -1
      output_saturation_positive: 550
      output_saturation_negative: -450
      output_deadband_positive: 305
      output_deadband_negative: -305
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    water:
      input_max_positive: 1
      output_saturation_positive: 500
      output_deadband_positive: 240
      deadband_thr: 0.001
      safe_value: 0
      timeout: 0.5

    drive_mode:
      safe_value: 0
      timeout: 0.5

  input_topics:
    carousel_ctrl:
      msg_type: "drill_msgs/CarouselCtrl"
      fields:
        swing: 'carousel_swing'
        index: 'carousel_index'

    tower_ctrl:
      msg_type: "drill_msgs/TowerCtrl"
      fields:
        vert_pins: 'tower_vert_pins'
        incl_pins: 'tower_incl_pins'
        tilt: 'tower_tilt'

    wrench_ctrl:
      msg_type: "drill_msgs/WrenchCtrl"
      fields:
        swing: 'wrench_swing'
        grip: 'wrench_grip'

    tracks_ctrl:
      msg_type: "drill_msgs/TracksCtrl"
      fields:
        left: 'tracks_left'
        right: 'tracks_right'

    arm_ctrl:
      msg_type: "drill_msgs/FloatCtrl"
      fields:
        ctrl: 'arm'

    fork_ctrl:
      msg_type: "drill_msgs/FloatCtrl"
      fields:
        ctrl: 'fork'

    drill_ctrl:
      msg_type: "drill_msgs/DrillActuatorCtrl"
      fields:
        feed_speed: "drill_feed_speed"
        rotation_speed: "drill_rotation_speed"
        feed_pressure: "drill_feed_pressure"
        torque_limit: "drill_torque_limit"

    air_ctrl:
      msg_type: "drill_msgs/AirCtrl"
      fields:
        power: 'air_power'
        enabled: 'air_enabled'

    dust_flaps_ctrl:
      msg_type: "drill_msgs/FloatCtrl"
      fields:
        ctrl: 'dust_flaps'

    jacks_ctrl:
      msg_type: "drill_msgs/JacksCtrl"
      fields:
        left: "jacks_left"
        right: "jacks_right"
        rear: "jacks_rear"

    water_ctrl:
      same_in_remote: true
      msg_type: "drill_msgs/FloatCtrl"
      fields:
        ctrl: 'water'

    mode_ctrl:
      msg_type: "drill_msgs/ModeCtrl"
      fields:
        drive: 'drive_mode'

can_decoder:
  rate: 1
  values:
    tower_angle:
      can_channel: '/can_6/tx'
      value_field: 'angle'
      protocol: 'inc1'
      source_id: 0xcf

    body_angle_x:
      can_channel: '/can_6/tx'
      value_field: 'angle_x'
      protocol: 'inc2'
      source_id: 0xce

    body_angle_y:
      can_channel: '/can_6/tx'
      value_field: 'angle_y'
      protocol: 'inc2'
      source_id: 0xce

    front_imu_pitch:
      can_channel: '/can_1/tx'
      value_field: 'pitch'
      protocol: 'ssi2'
      source_id: 131

    front_imu_roll:
      can_channel: '/can_1/tx'
      value_field: 'roll'
      protocol: 'ssi2'
      source_id: 131

    front_imu_acceleration_x:
      can_channel: '/can_1/tx'
      value_field: 'acceleration_x'
      protocol: 'adp'
      source_id: 131

    front_imu_acceleration_y:
      can_channel: '/can_1/tx'
      value_field: 'acceleration_y'
      protocol: 'adp'
      source_id: 131

    front_imu_acceleration_z:
      can_channel: '/can_1/tx'
      value_field: 'acceleration_z'
      protocol: 'adp'
      source_id: 131

    front_imu_magnetic_field_x:
      can_channel: '/can_1/tx'
      value_field: 'magnetic_field_x'
      protocol: 'mdp'
      source_id: 131

    front_imu_magnetic_field_y:
      can_channel: '/can_1/tx'
      value_field: 'magnetic_field_y'
      protocol: 'mdp'
      source_id: 131

    front_imu_magnetic_field_z:
      can_channel: '/can_1/tx'
      value_field: 'magnetic_field_z'
      protocol: 'mdp'
      source_id: 131

    front_imu_angular_rate_x:
      can_channel: '/can_1/tx'
      value_field: 'angular_rate_x'
      protocol: 'ardp'
      source_id: 131

    front_imu_angular_rate_y:
      can_channel: '/can_1/tx'
      value_field: 'angular_rate_y'
      protocol: 'ardp'
      source_id: 131

    front_imu_angular_rate_z:
      can_channel: '/can_1/tx'
      value_field: 'angular_rate_z'
      protocol: 'ardp'
      source_id: 131

    tower_imu_pitch:
      can_channel: '/can_6/tx'
      value_field: 'pitch'
      protocol: 'ssi2'
      source_id: 130

    tower_imu_roll:
      can_channel: '/can_6/tx'
      value_field: 'roll'
      protocol: 'ssi2'
      source_id: 130

    tower_imu_acceleration_x:
      can_channel: '/can_6/tx'
      value_field: 'acceleration_x'
      protocol: 'adp'
      source_id: 130

    tower_imu_acceleration_y:
      can_channel: '/can_6/tx'
      value_field: 'acceleration_y'
      protocol: 'adp'
      source_id: 130

    tower_imu_acceleration_z:
      can_channel: '/can_6/tx'
      value_field: 'acceleration_z'
      protocol: 'adp'
      source_id: 130

    tower_imu_magnetic_field_x:
      can_channel: '/can_6/tx'
      value_field: 'magnetic_field_x'
      protocol: 'mdp'
      source_id: 130

    tower_imu_magnetic_field_y:
      can_channel: '/can_6/tx'
      value_field: 'magnetic_field_y'
      protocol: 'mdp'
      source_id: 130

    tower_imu_magnetic_field_z:
      can_channel: '/can_6/tx'
      value_field: 'magnetic_field_z'
      protocol: 'mdp'
      source_id: 130

    tower_imu_angular_rate_x:
      can_channel: '/can_6/tx'
      value_field: 'angular_rate_x'
      protocol: 'ardp'
      source_id: 130

    tower_imu_angular_rate_y:
      can_channel: '/can_6/tx'
      value_field: 'angular_rate_y'
      protocol: 'ardp'
      source_id: 130

    tower_imu_angular_rate_z:
      can_channel: '/can_6/tx'
      value_field: 'angular_rate_z'
      protocol: 'ardp'
      source_id: 130

    rear_imu_pitch:
      can_channel: '/can_1/tx'
      value_field: 'pitch'
      protocol: 'ssi2'
      source_id: 129

    rear_imu_roll:
      can_channel: '/can_1/tx'
      value_field: 'roll'
      protocol: 'ssi2'
      source_id: 129

    rear_imu_acceleration_x:
      can_channel: '/can_1/tx'
      value_field: 'acceleration_x'
      protocol: 'adp'
      source_id: 129

    rear_imu_acceleration_y:
      can_channel: '/can_1/tx'
      value_field: 'acceleration_y'
      protocol: 'adp'
      source_id: 129

    rear_imu_acceleration_z:
      can_channel: '/can_1/tx'
      value_field: 'acceleration_z'
      protocol: 'adp'
      source_id: 129

    rear_imu_magnetic_field_x:
      can_channel: '/can_1/tx'
      value_field: 'magnetic_field_x'
      protocol: 'mdp'
      source_id: 129

    rear_imu_magnetic_field_y:
      can_channel: '/can_1/tx'
      value_field: 'magnetic_field_y'
      protocol: 'mdp'
      source_id: 129

    rear_imu_magnetic_field_z:
      can_channel: '/can_1/tx'
      value_field: 'magnetic_field_z'
      protocol: 'mdp'
      source_id: 129

    rear_imu_angular_rate_x:
      can_channel: '/can_1/tx'
      value_field: 'angular_rate_x'
      protocol: 'ardp'
      source_id: 129

    rear_imu_angular_rate_y:
      can_channel: '/can_1/tx'
      value_field: 'angular_rate_y'
      protocol: 'ardp'
      source_id: 129

    rear_imu_angular_rate_z:
      can_channel: '/can_1/tx'
      value_field: 'angular_rate_z'
      protocol: 'ardp'
      source_id: 129

    accel_pedal:
      can_channel: '/can_2/tx'
      value_field: 'accel_pedal'
      protocol: 'eec2'

    engine_load:
      can_channel: '/can_2/tx'
      value_field: 'engine_load'
      protocol: 'eec2'

    demand_torque:
      can_channel: '/can_2/tx'
      value_field: 'demand_torque'
      protocol: 'eec1'

    actual_torque:
      can_channel: '/can_2/tx'
      value_field: 'actual_torque'
      protocol: 'eec1'

    engine_speed:
      can_channel: '/can_2/tx'
      value_field: 'engine_speed'
      protocol: 'eec1'

    nominal_friction:
      can_channel: '/can_2/tx'
      value_field: 'nominal_friction'
      protocol: 'eec3'

    desired_engine_speed:
      can_channel: '/can_2/tx'
      value_field: 'desired_engine_speed'
      protocol: 'eec3'

    engine_hours:
      can_channel: '/can_2/tx'
      value_field: 'engine_hours'
      protocol: 'hours'

    engine_total_fuel_used:
      can_channel: '/can_2/tx'
      value_field: 'engine_total_fuel_used'
      protocol: 'lfc'

    engine_fuel_rate:
      can_channel: '/can_2/tx'
      value_field: 'engine_fuel_rate'
      protocol: 'lfe1'

    battery_potential:
      can_channel: '/can_2/tx'
      value_field: 'battery_potential'
      protocol: 'vep1'

    keyswitch_potential:
      can_channel: '/can_2/tx'
      value_field: 'keyswitch_potential'
      protocol: 'vep1'

    coolant_temperature:
      can_channel: '/can_2/tx'
      value_field: 'engine_coolant_temperature'
      protocol: 'et1'

    engine_fuel_temperature:
      can_channel: '/can_2/tx'
      value_field: 'engine_fuel_temperature'
      protocol: 'et1'

    engine_fuel_delivery_pressure:
      can_channel: '/can_2/tx'
      value_field: 'engine_fuel_delivery_pressure'
      protocol: 'eflp1'

    engine_oil_pressure:
      can_channel: '/can_2/tx'
      value_field: 'engine_oil_pressure'
      protocol: 'eflp1'

    engine_coolant_level:
      can_channel: '/can_2/tx'
      value_field: 'engine_coolant_level'
      protocol: 'eflp1'

    laser:
      can_channel: '/can_5/tx'
      value_field: 'laser'
      protocol: 'x90Encoders'

    rot_angle:
      can_channel: '/can_5/tx'
      value_field: 'encoder_1'
      protocol: 'x90Encoders'

    rotate_velocity:
      can_channel: '/can_5/tx'
      value_field: 'rotate_velocity'
      protocol: 'x90MastLvl'

    rotation_pressure:
      can_channel: '/can_5/tx'
      value_field: 'pressure_rotate'
      protocol: 'x90Prssr'

    feed_pressure:
      can_channel: '/can_5/tx'
      value_field: 'feed_pressure'
      protocol: 'x90Prssr'

    air_pressure:
      can_channel: '/can_5/tx'
      value_field: 'pressure_air'
      protocol: 'x90Prssr'

    fuel_level:
      can_channel: '/can_5/tx'
      value_field: 'level_fuel'
      protocol: 'x90WaterData'

    water_level:
      can_channel: '/can_5/tx'
      value_field: 'water_level'
      protocol: 'x90WaterData'

    jack_rear_left_len:
      can_channel: '/can_5/tx'
      value_field: 'uls_1'
      protocol: 'x90UlsJacks'

    jack_rear_right_len:
      can_channel: '/can_5/tx'
      value_field: 'uls_2'
      protocol: 'x90UlsJacks'

    jack_front_left_len:
      can_channel: '/can_5/tx'
      value_field: 'uls_3'
      protocol: 'x90UlsJacks'

    jack_front_right_len:
      can_channel: '/can_5/tx'
      value_field: 'uls_4'
      protocol: 'x90UlsJacks'

    rear_left_retracted:
      can_channel: '/can_5/tx'
      value_field: 'jack_b_1'
      protocol: 'x90JacksData'

    rear_right_retracted:
      can_channel: '/can_5/tx'
      value_field: 'jack_b_2'
      protocol: 'x90JacksData'

    front_left_retracted:
      can_channel: '/can_5/tx'
      value_field: 'jack_f_1'
      protocol: 'x90JacksData'

    front_right_retracted:
      can_channel: '/can_5/tx'
      value_field: 'jack_f_2'
      protocol: 'x90JacksData'

    rear_right_on_ground:
      can_channel: '/can_5/tx'
      value_field: 'jack_b_1_ground'
      protocol: 'x90JacksData'
      inverse: False

    rear_left_on_ground:
      can_channel: '/can_5/tx'
      value_field: 'jack_b_2_ground'
      protocol: 'x90JacksData'
      inverse: False

    front_left_on_ground:
      can_channel: '/can_5/tx'
      value_field: 'jack_f_1_ground'
      protocol: 'x90JacksData'

    front_right_on_ground:
      can_channel: '/can_5/tx'
      value_field: 'jack_f_2_ground'
      protocol: 'x90JacksData'

    vert_pin_cs_c:
      can_channel: '/can_5/tx'
      value_field: 'vert_pin_cs_c'
      protocol: 'x90PinsData'

    vert_pin_cs_o:
      can_channel: '/can_5/tx'
      value_field: 'vert_pin_cs_o'
      protocol: 'x90PinsData'

    vert_pin_ncs_c:
      can_channel: '/can_5/tx'
      value_field: 'vert_pin_ncs_c'
      protocol: 'x90PinsData'

    vert_pin_ncs_o:
      can_channel: '/can_5/tx'
      value_field: 'vert_pin_ncs_o'
      protocol: 'x90PinsData'

    tilt_pins_cs_c:
      can_channel: '/can_5/tx'
      value_field: 'tilt_pins_cs_c'
      protocol: 'x90PinsData'

    tilt_pins_cs_o:
      can_channel: '/can_5/tx'
      value_field: 'tilt_pins_cs_o'
      protocol: 'x90PinsData'

    tilt_pins_ncs_c:
      can_channel: '/can_5/tx'
      value_field: 'tilt_pins_ncs_c'
      protocol: 'x90PinsData'

    tilt_pins_ncs_o:
      can_channel: '/can_5/tx'
      value_field: 'tilt_pins_ncs_o'
      protocol: 'x90PinsData'

    arm_stage_1:
      can_channel: '/can_5/tx'
      value_field: 'stage_1'
      protocol: 'x90ArmData'

    arm_stage_2:
      can_channel: '/can_5/tx'
      value_field: 'stage_2'
      protocol: 'x90ArmData'

    fork_ccw:
      can_channel: '/can_5/tx'
      value_field: 'bracket_left'
      protocol: 'x90ForkData'

    fork_cw:
      can_channel: '/can_5/tx'
      value_field: 'bracket_right'
      protocol: 'x90ForkData'

    fork_len:
      can_channel: '/can_5/tx'
      value_field: 'bracket_dt_2'
      protocol: 'x90ForkData'

    carousel_index_1:
      can_channel: '/can_5/tx'
      value_field: 'carousel_1'
      protocol: 'x90CarouselData'

    carousel_index_2:
      can_channel: '/can_5/tx'
      value_field: 'carousel_2'
      protocol: 'x90CarouselData'

    carousel_main_len:
      can_channel: '/can_5/tx'
      value_field: 'carousel_main_axis_len'
      protocol: 'x90CarouselData'

    carousel_cup_1:
      can_channel: '/can_5/tx'
      value_field: 'bar_dt_1'
      protocol: 'x90BarData'

    carousel_cup_2:
      can_channel: '/can_5/tx'
      value_field: 'bar_dt_2'
      protocol: 'x90BarData'

    wrench_dt_1:
      can_channel: '/can_5/tx'
      value_field: 'wrench_dt_1'
      protocol: 'x90WrenchData'

    wrench_dt_2:
      can_channel: '/can_5/tx'
      value_field: 'wrench_dt_2'
      protocol: 'x90WrenchData'

    wrench_dt_3:
      can_channel: '/can_5/tx'
      value_field: 'wrench_dt_3'
      protocol: 'x90WrenchData'

    dust_flaps_c:
      can_channel: '/can_5/tx'
      value_field: 'dust_flaps_dt_2'
      protocol: 'x90DustFlaps'

    dust_flaps_o:
      can_channel: '/can_5/tx'
      value_field: 'dust_flaps_dt_1'
      protocol: 'x90DustFlaps'

  output_topics: # выходы из ноды, приём от устройства, паблишеры
    tower_inclinometer: # имя топика
      msg_type: "drill_msgs/Vector2d"
      fields:
        x: "tower_angle"

    board_inclinometer:
      msg_type: "drill_msgs/Vector2d"
      fields:
        x: "body_angle_x"
        y: "body_angle_y"

    front_imu:
      msg_type: "drill_msgs/IMU"
      fields:
        roll: "front_imu_roll"
        pitch: "front_imu_pitch"
        acceleration.x: "front_imu_acceleration_x"
        acceleration.y: "front_imu_acceleration_y"
        acceleration.z: "front_imu_acceleration_z"
        magnetic_field.x: "front_imu_magnetic_field_x"
        magnetic_field.y: "front_imu_magnetic_field_y"
        magnetic_field.z: "front_imu_magnetic_field_z"
        angular_rate.x: "front_imu_angular_rate_x"
        angular_rate.y: "front_imu_angular_rate_y"
        angular_rate.z: "front_imu_angular_rate_z"

    tower_imu:
      msg_type: "drill_msgs/IMU"
      fields:
        roll: "tower_imu_roll"
        pitch: "tower_imu_pitch"
        acceleration.x: "tower_imu_acceleration_x"
        acceleration.y: "tower_imu_acceleration_y"
        acceleration.z: "tower_imu_acceleration_z"
        magnetic_field.x: "tower_imu_magnetic_field_x"
        magnetic_field.y: "tower_imu_magnetic_field_y"
        magnetic_field.z: "tower_imu_magnetic_field_z"
        angular_rate.x: "tower_imu_angular_rate_x"
        angular_rate.y: "tower_imu_angular_rate_y"
        angular_rate.z: "tower_imu_angular_rate_z"

    rear_imu:
      msg_type: "drill_msgs/IMU"
      fields:
        roll: "rear_imu_roll"
        pitch: "rear_imu_pitch"
        acceleration.x: "rear_imu_acceleration_x"
        acceleration.y: "rear_imu_acceleration_y"
        acceleration.z: "rear_imu_acceleration_z"
        magnetic_field.x: "rear_imu_magnetic_field_x"
        magnetic_field.y: "rear_imu_magnetic_field_y"
        magnetic_field.z: "rear_imu_magnetic_field_z"
        angular_rate.x: "rear_imu_angular_rate_x"
        angular_rate.y: "rear_imu_angular_rate_y"
        angular_rate.z: "rear_imu_angular_rate_z"

    engine_state:
      msg_type: "drill_msgs/EngineState"
      fields:
        accel_pedal: "accel_pedal"
        engine_load: "engine_load"
        demand_torque: "demand_torque"
        actual_torque: "actual_torque"
        engine_speed: "engine_speed"
        nominal_friction: "nominal_friction"
        desired_engine_speed: "desired_engine_speed"
        engine_hours: "engine_hours"
        engine_total_fuel_used: "engine_total_fuel_used"
        engine_fuel_rate: "engine_fuel_rate"
        battery_potential: "battery_potential"
        keyswitch_potential: "keyswitch_potential"
        coolant_temperature: "coolant_temperature"
        engine_fuel_temperature: "engine_fuel_temperature"
        engine_fuel_delivery_pressure: "engine_fuel_delivery_pressure"
        engine_oil_pressure: "engine_oil_pressure"
        engine_coolant_level: "engine_coolant_level"

    drill_state_raw:
      msg_type: "drill_msgs/DrillStateRaw"
      fields:
        head_pos: 'laser'
        head_angular_pos: 'rot_angle'
        drill_rpm: 'rotate_velocity'
        rot_pressure: 'rotation_pressure'
        feed_pressure: 'feed_pressure'
        air_pressure: 'air_pressure'

    fuel_state_raw:
      msg_type: "drill_msgs/FloatStamped"
      fields:
        value: 'fuel_level'

    water_state_raw:
      msg_type: "drill_msgs/FloatStamped"
      fields:
        value: 'water_level'

    jacks_state_raw:
      msg_type: "drill_msgs/JacksStateRaw"
      fields:
        rear_left_len: 'jack_rear_left_len'
        rear_right_len: 'jack_rear_right_len'
        front_left_len: 'jack_front_left_len'
        front_right_len: 'jack_front_right_len'

    jacks_switch_state_raw:
      msg_type: "drill_msgs/JacksSwitchStateRaw"
      fields:
        front_left_retracted: 'front_left_retracted'
        front_right_retracted: 'front_right_retracted'
        rear_left_retracted: 'rear_left_retracted'
        rear_right_retracted: 'rear_right_retracted'
        front_left_on_ground: 'front_left_on_ground'
        front_right_on_ground: 'front_right_on_ground'
        rear_left_on_ground: 'rear_left_on_ground'
        rear_right_on_ground: 'rear_right_on_ground'

    vert_pins_state_raw:
      msg_type: "drill_msgs/PinsStateRaw"
      fields:
        right_open: 'vert_pin_ncs_o'
        right_locked: 'vert_pin_ncs_c'
        left_open: 'vert_pin_cs_o'
        left_locked: 'vert_pin_cs_c'

    incl_pins_state_raw:
      msg_type: "drill_msgs/PinsStateRaw"
      fields:
        right_open: 'tilt_pins_ncs_o'
        right_locked: 'tilt_pins_ncs_c'
        left_open: 'tilt_pins_cs_o'
        left_locked: 'tilt_pins_cs_c'

    arm_state_raw:
      msg_type: "drill_msgs/ArmStateRaw"
      fields:
        stage1_len: 'arm_stage_1'
        stage2_len: 'arm_stage_2'

    fork_state_raw:
      msg_type: "drill_msgs/ForkStateRaw"
      fields:
        ccw_limit: 'fork_ccw'
        cw_limit: 'fork_cw'
        length: 'fork_len'

    carousel_state_raw:
      msg_type: "drill_msgs/CarouselStateRaw"
      fields:
        index_1: 'carousel_index_1'
        index_2: 'carousel_index_2'
        cup_1: 'carousel_cup_1'
        cup_2: 'carousel_cup_2'
        length: 'carousel_main_len'

    wrench_state_raw:
      msg_type: "drill_msgs/WrenchStateRaw"
      fields:
        stage_1_len: 'wrench_dt_1'
        stage_2_len: 'wrench_dt_2'
        stage_3_len: 'wrench_dt_3'

    dust_flaps_state:
      msg_type: "drill_msgs/DustFlapsState"
      fields:
        open: 'dust_flaps_o'
        closed: 'dust_flaps_c'

state_tracker:
  rate: 20
  inc_roll_lpf:
    cutoff_freq: 10
  inc_pitch_lpf:
    cutoff_freq: 10
  body_rp_align:
    swap_rp: false
    roll_offset: 0
    pitch_offset: 0
    invert_roll: false
    invert_pitch: false
  front_imu_roll_lpf:
    cutoff_freq: 10
  front_imu_pitch_lpf:
    cutoff_freq: 10
  front_imu_rp_align:
    swap_rp: false
    roll_offset: 0
    pitch_offset: 15
    invert_roll: false
    invert_pitch: true
  rear_imu_roll_lpf:
    cutoff_freq: 10
  rear_imu_pitch_lpf:
    cutoff_freq: 10
  rear_imu_rp_align:
    swap_rp: false
    roll_offset: 0
    pitch_offset: 3.65
    invert_roll: true
    invert_pitch: false
  front_imu_roll_check:
    allowed_deviation: 3
  front_imu_pitch_check:
    allowed_deviation: 3
  rear_imu_roll_check:
    allowed_deviation: 5
  rear_imu_pitch_check:
    allowed_deviation: 5
  front_imu_to_lidar_rp_align:
    swap_rp: false
    roll_offset: 0
    pitch_offset: 0
    invert_roll: false
    invert_pitch: true
  rear_imu_to_lidar_rp_align:
    swap_rp: false
    roll_offset: 0
    pitch_offset: 0
    invert_roll: false
    invert_pitch: true
  yaw_lpf:
    cutoff_freq: 10
  x_lpf_for_speed:
    cutoff_freq: 1
  y_lpf_for_speed:
    cutoff_freq: 1
  yaw_lpf_for_speed:
    cutoff_freq: 2
  vx_lpf:
    cutoff_freq: 2
  vy_lpf:
    cutoff_freq: 2
  w_lpf:
    cutoff_freq: 4
  head_pos_transform:
    raw_1: 1532
    result_1: 0
    raw_2: 28969
    result_2: 19.9
  head_pos_raw_check:
    min_val: 1532
    max_val: 28972
  head_pos_lpf:
    cutoff_freq: 5
  head_speed_lpf:
    cutoff_freq: 4
  head_ang_pos_transform:
    raw_1: 0
    result_1: 0
    raw_2: 60
    result_2: 360
  head_rpm_transform:
    raw_1: 2.17
    result_1: 1
    raw_2: 4.64
    result_2: 110
  feed_pressure_transform:
    raw_1: 2.4497
    result_1: 185.1242
    raw_2: 3.1659
    result_2: 248.3492
  rot_pressure_transform:
    raw_1: 2.717
    result_1: 209.187
    raw_2: 3.845
    result_2: 311.72
  air_pressure_transform:
    raw_1: 0.19379
    result_1: 0.358527
    raw_2: 0.71474
    result_2: 3.864537

rtk_connector:
  rate: 20

driller:
  rate: 20.0
  user_feed_pressure: 80.0
  user_rotation_speed: 115.0
  timeouts:
    drill_state: 0.5
    rock_type: 5.0
    driller_action: 1.0

main_state_machine:
  rate: 10
  no_move_allowed_err: 0.2
  engine_idle_timeout: 30 # time in seconds to wait before low down engine rpm in Idle state
  wbl_delay: 4.0 # wait before level

  nominal_speed: 0.4 # m/s
  min_travel_speed: 0.1

  curvature_est_window: 2.0

  maneuver_builder_params:
    min_turn_radius: 2.0
    straight_part_len: 2
    straight_part_len_special: 5
    endpoint_move_step: 1
    endpoint_move_limit: 5
    path_point_step: 0.2
    num_threads: 6
    rrt_max_iterations: 30000
    turn_penalty: 1.0
    close_search_cutoff: 2
    search_radius: 4
    buffer_dist: 13
    yaw_inter_factor: 3
    yaw_res: 0.2243
    xy_res: 0.3
    yaw_err_penalty: 1
    border_safety_dist: 0.5
    obst_safety_dist: 0.2
    rrt_goal_bias: 0.2
    rrt_step_size: 0.25
    rrt_goal_sample_rate: 0.03
    dubins_turn_threshold: 1.57
    obstacle_radius: 0.8

remote_connector:
  rate: 1

modbus_node:
  rate: 10
  method: 'rtu'
  port: "/Users/<USER>/ttyXR1"  # Сохраняем существующий порт
  baudrate: 115200
    
  ios:
    # МПР (Fuse Module) - 8 каналов защиты
    fuse_module_status:
      unit: 0x12        # ID модуля предохранителей
      address: 0
      count: 8
      type: "di"       # discrete input - чтение статуса предохранителей
      readable: true
      writable: false

    fuse_channel_control:  # Переименовано из fuse_lamp_control - управляет всеми каналами, а не только лампами
      unit: 0x12
      address: 0
      count: 8
      type: "co"       # coil - управление каналами
      readable: false
      writable: true
      
    # БВРР (RoboMode Selector) - 26 каналов DPDT
    robomode_read:
      unit: 0x0A
      address: 0
      count: 26        # 26 каналов согласно документации
      type: "di"       # discrete input - чтение состояния каналов
      readable: true
      writable: false
      
    robomode_set:
      unit: 0x0A
      address: 0
      count: 26        # 26 каналов согласно документации
      type: "co"       # coil - управление каналами
      readable: false
      writable: true
      
    # ИБП (UPS)
    ups_status:
      unit: 0x30
      address: 0
      count: 5         # Ток АКБ, Ток сети, Напряжение АКБ, Напряжение сети, Ток заряда
      type: "ir"       # input register
      readable: true
      writable: false
      
    ups_discrete_status:
      unit: 0x30
      address: 0
      count: 6         # АКБ активна, Напряжение АКБ в диапазоне, Превышение тока АКБ, 
                       # Сеть активна, Напряжение сети в диапазоне, Превышение тока сети
      type: "di"       # discrete input
      readable: true
      writable: false
      
    ups_control:
      unit: 0x30
      address: 0
      count: 2         # Два управляющих регистра согласно документации:
                       # 0: Вкл. заряд
                       # 1: Вкл. питание от АКБ
      type: "co"       # coil
      readable: false
      writable: true
      
  output_topics:
    fuse_status:
      msg_type: "std_msgs/UInt16MultiArray"
      fields:
        data:
          io: "fuse_module_status"
          word: "ALL"
          
    robomode_feedback:
      msg_type: "drill_msgs/BoolStamped"
      fields:
        value:
          io: "robomode_read"
          word: 0
          
    ups_status:
      msg_type: "drill_msgs/UpsStatus"
      fields:
        batt_current:
          io: "ups_status"
          word: 0
          scale: 0.1   # Масштабирование согласно документации
        grid_current:
          io: "ups_status"
          word: 1
          scale: 0.1
        batt_voltage:
          io: "ups_status"
          word: 2
          scale: 0.1
        grid_voltage:
          io: "ups_status"
          word: 3
          scale: 0.1
        charge_current:   # Добавлен ток заряда
          io: "ups_status"
          word: 4
          scale: 0.1
        batt_active:
          io: "ups_discrete_status"  # Изменен IO source
          word: 0
        grid_active:
          io: "ups_discrete_status"  # Изменен IO source
          word: 3
        batt_voltage_ok:  # Переименовано для соответствия документации
          io: "ups_discrete_status"
          word: 1
        grid_voltage_ok:  # Переименовано для соответствия документации
          io: "ups_discrete_status"
          word: 4
        batt_current_exceeded:  # Добавлен новый статус
          io: "ups_discrete_status"
          word: 2
        grid_current_exceeded:  # Добавлен новый статус
          io: "ups_discrete_status"
          word: 5
          
  input_topics:
    robomode_control:
      msg_type: "drill_msgs/BoolStamped"
      timeout: 0
      fields:
        value:
          io: "robomode_set"
          word: 0
          latch: false
          default: false

    lamp_control:
      msg_type: "drill_msgs/LampCtrl"
      timeout: 0
      fields:
        front_yellow:
          io: "fuse_channel_control"  # Обновлено имя IO
          word: 0
          latch: false
          default: false
        front_red:
          io: "fuse_channel_control"  # Обновлено имя IO
          word: 1
          latch: false
          default: false
        front_blue:
          io: "fuse_channel_control"  # Обновлено имя IO
          word: 2
          latch: false
          default: false
        rear_yellow:
          io: "fuse_channel_control"  # Обновлено имя IO
          word: 3
          latch: false
          default: false
        rear_red:
          io: "fuse_channel_control"  # Обновлено имя IO
          word: 4
          latch: false
          default: false
        rear_blue:
          io: "fuse_channel_control"  # Обновлено имя IO
          word: 5
          latch: false
          default: false

    lights_control:
      msg_type: "drill_msgs/BoolStamped"
      timeout: 0
      fields:
        value:
          io: "fuse_channel_control"  # Обновлено имя IO
          word: 6
          latch: false
          default: true

    ups_charge_control:
      msg_type: "drill_msgs/BoolStamped"
      timeout: 0
      fields:
        value:
          io: "ups_control"
          word: 0        # Вкл. заряд (регистр 0)
          latch: true
          default: true

    ups_battery_power_control:
      msg_type: "drill_msgs/BoolStamped"
      timeout: 0
      fields:
        value:
          io: "ups_control"
          word: 1        # Вкл. питание от АКБ (регистр 1)
          latch: true
          default: false

tracks_regulator:
  rate: 20
  pid:
    left:
      p: 1.5
      i: 0.8
      d: 0.15
      ff: 0.125
      i_saturation: 0.4
      out_min: -1.0
      out_max: 1.0
      d_term_tc: 0.3
      out_tc: 0.3
      force_zero: true
    right:
      p: 1.5
      i: 0.8
      d: 0.15
      ff: 0.125
      i_saturation: 0.4
      out_min: -1.0
      out_max: 1.0
      d_term_tc: 0.3
      out_tc: 0.3
      force_zero: true
  timeout: 1.0

path_follower:
  rate: 20
  # Path following control parameters
  lookahead_distance: 0.8                    # Lookahead distance for path following (m)
  lateral_correction_gain: 1.2               # Proportional gain for lateral error correction
  lateral_correction_limit: 0.6              # Maximum lateral correction angle (rad)
  heading_error_gain: 1.5                    # Proportional gain for heading error control
  path_curvature_feedforward_gain: 0.4       # Feedforward gain for path curvature compensation

  # Turn rate limiting parameters
  turn_rate_base_limit: 4.0                  # Base limit for turn rate (rad/s)
  turn_rate_speed_factor: 1.0                # Speed-dependent turn rate factor (rad/s per m/s)
  max_heading_error_for_full_speed: 1.047    # Max heading error for full speed operation (rad, 60°)

  # Approach control parameters
  max_approach_speed: 0.078                  # Maximum speed in approach mode (m/s)
  max_approach_angular_error_deg: 9          # Angular error threshold for speed reduction (degrees) [FIXED from ROS1]
  goal_achievement_radius: 0.06              # Goal achievement zone radius (m)
  approach_max_turn_rate: 0.015              # Maximum turn rate in approach mode (rad/s) [FIXED from ROS1]
  max_allowed_distance_to_target: 5.0        # Maximum allowed distance (m) [RELAXED: was too strict]
  approach_timeout: 60.0                     # Maximum time for approach mode (s)

  # Approach PID parameters [FIXED to match proven ROS1 values]
  approach_pid:
    p: 0.15                                  # Proportional gain [FIXED from ROS1: was 0.12]
    i: 0.00                                  # Integral gain [FIXED from ROS1: was 0.02]
    d: 0.05                                  # Derivative gain [FIXED from ROS1: was 0.01]
    i_saturation: 0.01                       # Integral windup protection [FIXED from ROS1: was 0.05]

  # Safety parameters (used in both path following and approach)
  max_heading_error_for_motion: 1.57         # Maximum heading error for forward/backward motion (rad, 90°)

  # Control timing parameters
  message_timeout: 0.5                       # Message time-to-live for safety (s)
  output_smoothing_time_constant: 0.02       # Output time constant for control smoothing (s)

  # RMS tracking parameters
  rms_recent_time_constant: 7.0              # Time constant for recent RMS tracking (s)
  # Sensor offset estimation parameters
  sensor_offset_smoothing_alpha: 0.15        # Smoothing factor for sensor offset updates (0.1-0.5)
  sensor_offset_min_angular_velocity: 0.05   # Minimum angular velocity for reliable offset calculation (rad/s)
  sensor_offset_max_reasonable_offset: 2.0   # Maximum reasonable sensor offset limit (m)
  sensor_offset_quality_log_period: 10.0     # Period for logging offset estimation quality (s)
  sensor_offset_min_updates_for_reliability: 15   # Minimum successful updates needed for reliable estimate
  sensor_offset_max_stability_variance: 0.01      # Maximum variance in recent estimates for stable tracking (m²)
  sensor_offset_reliability_threshold: 0.7        # Minimum confidence for considering estimate reliable

hal_connector:
  rate: 10.0                    # Frequency of main work timer (Hz)
  mqtt_host: "localhost"        # MQTT broker host for HAL communication
  mqtt_port: 1883              # MQTT broker port
  message_timeout: 0.5         # Timeout for ROS message freshness (s)
  log_period: 5.0              # Period for repeated log messages to avoid spam (s)
  hal_heartbeat_timeout: 10.0  # Timeout for HAL heartbeat messages (s)

leveler:
  # Control loop rate
  rate: 10
  
  # Speed limits and control parameters
  max_jacks_speed: 1.0
  angles_time_thr: 0.6
  
  # Pulling control parameters
  pulling_speed: 0.8
  pulling_speed_front: 0.8
  pulling_speed_rear: 0.8
  pulling_down_speed_on_ground: 0.1
  pulling_ang_err_to_restore: 1.0
  
  # PID parameters for pulling state
  pull_jacks_speed_p: 5.0
  pull_jacks_speed_i: 0.04
  pull_jacks_speed_d: 2.0
  pull_jacks_i_limit: 20.0
  
  # Touchdown control parameters
  touchdown_jacks_control_front: 0.3
  touchdown_jacks_control_back: 0.3
  
  # Lift control parameters
  common_lift_control: 0.2
  min_lift_height: 0.09
  jacks_sync_p: 3.0
  
  # Leveling control parameters
  leveling_roll_p: 9.0
  leveling_pitch_p: 8.0
  max_level_speed: 0.7
  leveling_ctrl_jacks_integral_min: 0.5
  
  # Final leveling parameters
  final_level_ctrl: 0.15
  
  # Dead zone parameters
  holding_dead_zone: 0.5
  holding_dead_zone_hist_high: 0.21
  holding_dead_zone_hist_low: 0.1
  
  # Timing parameters
  required_stab_time: 3.0
  
  # Safety parameters
  max_roll_above_start: 1.5
  max_pitch_above_start: 2.0

  # Mapping/alignment (safety defaults)
  invert_roll: true      # Flip roll sign in kinematics (right-down => extend right)
  invert_pitch: false
  swap_left_right: false

  invert_roll: false

depth_tracker:
drill_regulator_node:
  rate: 50.0
  msg_timeout: 0.5
  pdf_free_moving: 0.1
  default_rot_torque: 0.5

  feed_reg:
    p: 1.0
    i: 0.5
    d: 0.1
    ff: 0.0
    i_saturation: 1.0
    out_min: -1.0
    out_max: 1.0
    d_term_tc: 0.01
    out_tc: 0.01

  press_reg:
    p: 0.8
    i: 0.3
    d: 0.05
    ff: 0.0
    i_saturation: 1.0
    out_min: -1.0
    out_max: 1.0
    d_term_tc: 0.01
    out_tc: 0.01

  rot_reg:
    p: 1.2
    i: 0.4
    d: 0.08
    ff: 0.0
    i_saturation: 1.0
    out_min: -1.0
    out_max: 1.0
    d_term_tc: 0.01
    out_tc: 0.01
  rate: 20.0
  max_reasonable_delta: 0.10
  drilling_pressure_threshold: 0.5
  remote_pressure_threshold: 1.0
  subscription_timeout: 0.5
  remote_pressure_stable_time_s: 1.0
  drilling_pressure_stable_time_s: 0.5
  remote_new_session_min_distance_m: 1.0
  max_wellhead_below_drill_m: 2.5