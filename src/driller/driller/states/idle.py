from base_node.base_fsm import BaseState


class IdleState(BaseState):
    """Idle state - waiting for drilling command.

    Key responsibilities:
    - Reset all controls to zero
    - Reset counters and flags
    - Wait for new driller_action
    - Validate depth data before starting
    - Choose next state based on first_rod_flag
    """

    def __init__(self, node):
        super().__init__(name='idle', node=node)

    def on_transition_to(self):
        """Actions on entering idle state"""
        # Stop all controls
        self.node.stop_control()

        # Reset counters and flags
        self.node.short_pullup_cnt = getattr(self.node, 'short_pullup_cnt', 0)
        self.node.last_mandatory_pullup_spindle_depth = None

        # Reset stuck detection
        if hasattr(self.node, 'safety_manager'):
            self.node.safety_manager.last_unstuck_time = self.node.get_time()

    def do_work(self):
        """Main work cycle - check for new action and validate before starting"""
        # Check if we have a new action
        if not self.node.current_action:
            return

        # Validate depth data before starting any drilling operation
        if not self.node.safety_manager.validate_depth_data():
            self.node.handle_error(
                "Cannot start drilling - invalid depth data",
                level=self.node.WARN
            )
            return

        # Store ground depth for first rod
        if self.node.current_action.first_rod_flag and self.node.subs.drill_state:
            self.node.ground_spindle_depth = float(self.node.subs.drill_state.head_pos)

        # Choose next state based on first_rod_flag
        if self.node.current_action.first_rod_flag:
            # First rod requires ground detection
            self.node.set_state('touchdown')
        else:
            # Additional rods go straight to drilling
            self.node.set_state('drilling')


