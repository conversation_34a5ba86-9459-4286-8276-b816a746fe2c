from base_node.base_fsm import BaseState
from base_node.base_node.pid import PID


class DrillingState(BaseState):
    def __init__(self, node):
        super().__init__(name='drilling', node=node)
        self.entry_time = 0.0
        self.entry_spindle_depth = None
        self.pid = PID(
            get_time=self.node.get_time,
            node=self.node,
            p=0.0,
            i=10.0,
            d=0.0,
            i_saturation=50.0,
            out_min=-50.0,
            out_max=50.0,
            name="DrillSpeedPID",
            enable_logging=False,
        )

    def on_transition_to(self):
        self.entry_time = self.node.get_time()
        if self.node.subs.drill_state:
            self.entry_spindle_depth = self.node.subs.drill_state.head_pos
        self.pid.reset()

    def do_work(self):
        params = self.node.profile_manager.current_params.get('drilling', {})

        # 1) target depth
        act = self.node.current_action
        ds = self.node.subs.drill_state
        if act and ds and ds.head_pos >= float(act.drill_spindle_depth):
            self.node.set_state('wait_after_drill')
            return

        # 2) Check for stuck conditions
        if self.node.safety_manager.is_stuck:
            self.node.set_state('unstuck')
            return

        if self.node.safety_manager.is_hard_rot:
            self.node.set_state('hard_rot')
            return

        # 3) Mandatory pullups from depth tables
        pullup_action = self._check_mandatory_pullups(params.get('mandatory_pullups', []))
        if pullup_action:
            self.node.set_state('pullup')
            # Store pullup action for PullUpState to use
            self.node.pullup_action = pullup_action
            return

        # 4) Air-based pullup trigger
        if not self.node.safety_manager.check_air_system():
            self.node.set_state('pullup')
            self.node.pullup_action = 'air'  # Air-triggered pullup
            return

        # 5) I-regulator for feed pressure
        i_gain = params.get('drilling_speed_i_gain', 10.0)
        i_max = params.get('drilling_speed_i_max', 50.0)
        i_min = params.get('drilling_speed_i_min', -50.0)
        self.pid.i = i_gain
        self.pid.i_saturation = abs(i_max)
        self.pid.out_min = i_min
        self.pid.out_max = i_max
        target_speed = params.get('target_drill_speed', 0.05)
        cur_speed = self.node.safety_manager.drill_speed_smoothed or 0.0
        correction = self.pid.update(target_speed, cur_speed)
        base_pressure = float(self.node.node_params.get('user_feed_pressure', 80.0))
        psp = base_pressure + correction

        # reduce when rotation pressure high
        high_thr = params.get('high_rp_threshold', 250.0)
        reduced = params.get('feed_pressure_reduced', 60.0)
        if ds and float(ds.rot_pressure) > high_thr:
            psp = min(psp, reduced)

        # min limit
        psp = max(psp, float(params.get('feed_pressure_min', 20.0)))

        # depth table max
        max_tbl = params.get('feed_pressure_limit', [])
        if max_tbl:
            max_allowed = self.node.profile_manager.get_value_for_current_depth(max_tbl, default=psp)
            psp = min(psp, float(max_allowed))

        # slow ramp up on enter
        if not hasattr(self, '_last_t'):
            self._last_t = self.node.get_time()
        t = self.node.get_time()
        dt = t - getattr(self, '_last_t', t)
        self._last_t = t
        rate = float(params.get('slow_press_increase_rate', 20.0))
        slow_target = (self.node.ctrl_drill.feed_pressure or 0.0) + rate * dt
        self.node.ctrl_drill.feed_pressure = min(psp, slow_target)

        # 6) Water control from depth tables
        water_ctrl = self.node.profile_manager.get_value_for_current_depth(
            params.get('water_ctrl', []), default=0.3
        )
        self.node.ctrl_water.ctrl = float(water_ctrl)

        # 7) Other controls
        self.node.ctrl_drill.feed_speed = float(params.get('feed_speed_ctrl', 1.0))
        # rotation speed limit by depth with vibration safety check
        rot_tbl = params.get('rotation_speed_limit', [])
        max_rot = self.node.profile_manager.get_value_for_current_depth(rot_tbl, default=115.0)
        user_rot = float(self.node.node_params.get('user_rotation_speed', 115.0))
        desired_rotation = float(min(user_rot, float(max_rot)))
        self.node.set_safe_rotation(desired_rotation)

        # Air power and dust collector control
        self.node.ctrl_air.power = float(params.get('air_power_ctrl', 1.0))
        self.node.ctrl_dust_collector.value = bool(params.get('dust_ctrl', True))

        # Set control modes
        self.node.ctrl_drill.feed_speed_is_raw = False  # Normalized
        self.node.ctrl_drill.rotation_speed_is_raw = True  # Absolute RPM
        self.node.ctrl_drill.feed_pressure_is_raw = True  # Absolute pressure

        # Check for transition to soft drilling mode
        if self._should_enter_soft_mode(params):
            self.node.set_state('pass_soft')
            return

    def _check_mandatory_pullups(self, pullup_list):
        """Check if mandatory pullup is required based on depth tables"""
        if not pullup_list or not self.node.subs.drill_state:
            return None

        current_depth = float(self.node.subs.drill_state.head_pos)

        for pullup_entry in pullup_list:
            depth_ref = pullup_entry.get('depth', {})
            action = pullup_entry.get('action', 'short')

            # Convert depth reference to absolute spindle depth
            threshold_depth = self.node.profile_manager._convert_depth_reference(depth_ref)

            if threshold_depth is None:
                continue

            # Check if we've reached this pullup depth
            if current_depth >= threshold_depth:
                # Check if we haven't already done this pullup
                if (self.node.last_mandatory_pullup_spindle_depth is None or
                    current_depth > self.node.last_mandatory_pullup_spindle_depth):

                    self.node.last_mandatory_pullup_spindle_depth = current_depth
                    self.node.log(f"Mandatory pullup triggered at depth {current_depth:.3f}m, action: {action}")
                    return action

        return None

    def _should_enter_soft_mode(self, params):
        """Check if we should enter soft drilling mode based on conditions"""
        # Check for low rotation pressure indicating soft formation
        ds = self.node.subs.drill_state
        if not ds:
            return False

        soft_pressure_threshold = float(params.get('soft_pressure_threshold', 100.0))
        current_rot_pressure = float(ds.rot_pressure)

        # Enter soft mode if rotation pressure is consistently low
        if current_rot_pressure < soft_pressure_threshold:
            # Additional checks can be added here (time in low pressure, etc.)
            return True

        return False


