from base_node.base_fsm import BaseState


class TouchdownState(BaseState):
    """Touchdown state - ground detection for first rod.

    Key responsibilities:
    - Slow feed down with no rotation
    - Detect ground contact by speed reduction
    - Store ground depth for future reference
    - Transition to overburden_pass when ground detected
    """

    def __init__(self, node):
        super().__init__(name='touchdown', node=node)
        self.entry_time = 0.0
        self.entry_spindle_depth = None

    def on_transition_to(self):
        """Actions on entering touchdown state"""
        self.entry_time = self.node.get_time()
        if self.node.subs.drill_state:
            self.entry_spindle_depth = self.node.subs.drill_state.head_pos

    def do_work(self):
        """Main work cycle - slow feed down and detect ground contact"""
        params = self.node.profile_manager.current_params.get('touchdown', {})
        common = self.node.profile_manager.current_params.get('common', {})

        # Get current drill state
        ds = self.node.subs.drill_state
        if not ds:
            return

        # Set controls for slow touchdown
        self.node.ctrl_drill.feed_speed = float(params.get('feed_speed_ctrl', 0.2))
        self.node.ctrl_drill.rotation_speed = float(params.get('rotation_speed_ctrl', 0.0))
        self.node.ctrl_drill.feed_pressure = float(params.get('feed_pressure_ctrl', 20.0))

        # Set raw mode for precise control
        self.node.ctrl_drill.feed_speed_is_raw = False  # Normalized control
        self.node.ctrl_drill.rotation_speed_is_raw = False
        self.node.ctrl_drill.feed_pressure_is_raw = True  # Absolute pressure

        # Air and water controls
        self.node.ctrl_air.power = float(params.get('air_power_ctrl', 0.0))  # No air initially
        self.node.ctrl_water.ctrl = float(params.get('water_ctrl', 0.0))  # No water initially

        # Dust collector control
        self.node.ctrl_dust_collector.value = bool(params.get('dust_ctrl', False))

        # Ground detection logic (time + depth delta + command validation)
        speed_thr_cmd = float(common.get('ground_detect_feed_speed_thr', 0.3))
        detect_duration = float(common.get('ground_detect_duration', 1.0))
        depth_delta_req = float(params.get('depth_delta', 0.05))
        min_touch_time = float(params.get('min_touchdown_time', 0.5))

        time_in_state = self.node.get_time() - self.entry_time

        # Reset ground detection timer when feed speed is above threshold (moving fast = not at ground)
        if ds.head_speed > speed_thr_cmd:
            self._ground_detect_started = self.node.get_time()
        else:
            # initialize timer once
            if not hasattr(self, '_ground_detect_started'):
                self._ground_detect_started = self.node.get_time()

        # Conditions: enough time at low speed, sufficient depth delta, and commanded feed is forward
        depth_traveled = 0.0
        if self.entry_spindle_depth is not None:
            depth_traveled = ds.head_pos - self.entry_spindle_depth

        low_speed_time = self.node.get_time() - getattr(self, '_ground_detect_started', self.entry_time)
        cmd_forward_ok = float(self.node.ctrl_drill.feed_speed) > 0.3

        if (
            time_in_state >= min_touch_time and
            low_speed_time > detect_duration and
            depth_traveled >= depth_delta_req and
            cmd_forward_ok
        ):
            # Ground detected - store ground depth
            self.node.ground_spindle_depth = ds.head_pos
            self.node.log(f"Ground detected at depth {self.node.ground_spindle_depth:.3f}m")
            self.node.set_state('overburden_pass')
            return

        # Safety check - maximum touchdown depth
        max_depth = float(params.get('max_touchdown_depth', 2.0))
        if self.entry_spindle_depth is not None:
            depth_traveled = ds.head_pos - self.entry_spindle_depth
            if depth_traveled > max_depth:
                # Assume ground at current position if we've gone too far
                self.node.ground_spindle_depth = ds.head_pos
                self.node.log(f"Max touchdown depth reached, assuming ground at {self.node.ground_spindle_depth:.3f}m")
                self.node.set_state('overburden_pass')
                return


