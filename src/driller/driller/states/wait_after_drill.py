from base_node.base_fsm import BaseState


class WaitAfterDrillState(BaseState):
    """Wait after drill state - preparation for drill raise.

    Key responsibilities:
    - Stop drilling operations
    - Prepare for drill raise
    - Clean hole with air/water
    - Transition to raise state when ready
    """

    def __init__(self, node):
        super().__init__(name='wait_after_drill', node=node)
        self.entry_time = 0.0
        self.entry_spindle_depth = None

    def on_transition_to(self):
        """Actions on entering wait after drill state"""
        self.entry_time = self.node.get_time()
        if self.node.subs.drill_state:
            self.entry_spindle_depth = self.node.subs.drill_state.head_pos

        self.node.log(f"Drilling complete at depth {self.entry_spindle_depth:.3f}m, preparing for raise")

    def do_work(self):
        """Main work cycle - prepare for raise"""
        params = self.node.profile_manager.current_params.get('wait_after_drill', {})

        # Get current drill state
        ds = self.node.subs.drill_state
        if not ds:
            return

        # Stop feed movement
        self.node.ctrl_drill.feed_speed = 0.0

        # Continue rotation for hole cleaning
        self.node.ctrl_drill.rotation_speed = float(params.get('cleaning_rotation_speed', 60.0))

        # Additional controls
        self.node.ctrl_drill.feed_pressure = float(params.get('feed_pressure_ctrl', 0.0))
        self.node.ctrl_air.power = float(params.get('air_power_ctrl', 1.0))
        self.node.ctrl_dust_collector.value = bool(params.get('dust_ctrl', True))

        # Reduced pressure for cleaning
        self.node.ctrl_drill.feed_pressure = float(params.get('cleaning_feed_pressure', 20.0))

        # Set control modes
        self.node.ctrl_drill.feed_speed_is_raw = False  # Normalized
        self.node.ctrl_drill.rotation_speed_is_raw = True  # Absolute RPM
        self.node.ctrl_drill.feed_pressure_is_raw = True  # Absolute pressure

        # Full air for hole cleaning
        self.node.ctrl_air.power = float(params.get('cleaning_air_power', 1.0))

        # Water for cleaning
        self.node.ctrl_water.ctrl = float(params.get('cleaning_water_ctrl', 0.5))

        # Wait for cleaning time before transitioning to raise
        cleaning_time = float(params.get('cleaning_time', 3.0))
        time_in_state = self.node.get_time() - self.entry_time

        if time_in_state >= cleaning_time:
            self.node.log("Hole cleaning complete, starting raise")
            self.node.set_state('raise')
            return


