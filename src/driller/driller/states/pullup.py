from base_node.base_fsm import BaseState


class PullUpState(BaseState):
    """Pullup state - drill pullup for cleaning and unsticking.

    Key responsibilities:
    - Execute different types of pullups (short, long, cracked, air)
    - Manage water flow during pullup
    - Track pullup counters
    - Transition to after_pullup when complete
    """

    def __init__(self, node):
        super().__init__(name='pullup', node=node)
        self.entry_time = 0.0
        self.entry_spindle_depth = None
        self.pullup_phase = 'lift'  # 'lift', 'pause', 'complete'
        self.pause_start_time = 0.0

    def on_transition_to(self):
        """Actions on entering pullup state"""
        self.entry_time = self.node.get_time()
        if self.node.subs.drill_state:
            self.entry_spindle_depth = self.node.subs.drill_state.head_pos

        self.pullup_phase = 'lift'
        self.pause_start_time = 0.0

        # Save and stop water like ROS1. Save current if not already remembered this cycle
        if getattr(self.node, 'saved_water_restored', True):
            self.node.saved_water_ctrl = float(getattr(self.node.subs, 'water_ctrl', getattr(self.node.ctrl_water, 'ctrl', 0.0)) or 0.0)
            self.node.saved_water_restored = False
        self.node.ctrl_water.ctrl = 0.0

        # Determine pullup type
        pullup_action = getattr(self.node, 'pullup_action', 'short')
        self.node.log(f"Starting {pullup_action} pullup from depth {self.entry_spindle_depth:.3f}m")

        # Update counters
        if pullup_action == 'short':
            self.node.short_pullup_cnt += 1

    def do_work(self):
        """Main work cycle - execute pullup sequence"""
        params = self.node.profile_manager.current_params.get('pullup', {})
        pullup_action = getattr(self.node, 'pullup_action', 'short')

        # Get current drill state
        ds = self.node.subs.drill_state
        if not ds:
            return

        # Dynamic short pullup distance based on depth
        act = self.node.current_action
        first_rod = bool(getattr(act, 'first_rod_flag', False)) if act else False
        if first_rod and self.entry_spindle_depth is not None:
            entry_from_ground = float(self.entry_spindle_depth) - float(self.node.ground_spindle_depth)
            thr = float(params.get('depth_threshold', 1.0))
            short_low = float(params.get('short_pullup_height_low', 0.6))
            short_high = float(params.get('short_pullup_height_high', 1.0))
            dynamic_short = short_low if entry_from_ground < thr else short_high
        else:
            dynamic_short = float(params.get('short_pullup_height_high', 1.0))

        # Get pullup parameters based on action type
        if pullup_action == 'long':
            lift_distance = float(params.get('long_pullup_distance', 2.0))
            pause_time = float(params.get('long_pullup_pause_time', 3.0))
            water_ctrl = float(params.get('long_pullup_water_ctrl', 0.8))
        elif pullup_action == 'cracked':
            lift_distance = float(params.get('cracked_pullup_distance', 1.0))
            pause_time = float(params.get('cracked_pullup_pause_time', 2.0))
            water_ctrl = float(params.get('cracked_pullup_water_ctrl', 0.6))
        elif pullup_action == 'air':
            lift_distance = float(params.get('air_pullup_distance', 1.5))
            pause_time = float(params.get('air_pullup_pause_time', 2.5))
            water_ctrl = float(params.get('air_pullup_water_ctrl', 0.7))
        else:  # short pullup
            lift_distance = float(params.get('short_pullup_distance', dynamic_short))
            pause_time = float(params.get('short_pullup_pause_time', 1.5))
            water_ctrl = float(params.get('short_pullup_water_ctrl', 0.4))

        current_depth = float(ds.head_pos)

        if self.pullup_phase == 'lift':
            # Lifting phase
            target_depth = self.entry_spindle_depth - lift_distance

            if current_depth <= target_depth:
                # Reached target lift depth, start pause
                self.pullup_phase = 'pause'
                self.pause_start_time = self.node.get_time()
                self.node.log(f"Pullup lift complete, pausing for {pause_time:.1f}s")
            else:
                # Continue lifting with speed depending on depth
                if first_rod:
                    current_from_ground = float(current_depth) - float(self.node.ground_spindle_depth)
                    slow_depth = float(params.get('slow_pullup_depth', 0.6))
                    v_fast = -float(params.get('feed_speed_normal_ctrl', 0.9))
                    v_slow = -float(params.get('feed_speed_reduced_ctrl', 0.5))
                    self.node.ctrl_drill.feed_speed = v_fast if current_from_ground > slow_depth else v_slow
                else:
                    self.node.ctrl_drill.feed_speed = -float(params.get('feed_speed_normal_ctrl', 0.9))

        elif self.pullup_phase == 'pause':
            # Pause phase at top
            pause_elapsed = self.node.get_time() - self.pause_start_time

            if pause_elapsed >= pause_time:
                # Pause complete
                self.pullup_phase = 'complete'
                self.node.log("Pullup pause complete, transitioning to after_pullup")
            else:
                # Hold position during pause
                self.node.ctrl_drill.feed_speed = 0.0

        elif self.pullup_phase == 'complete':
            # Pullup complete, transition to after_pullup
            self.node.set_state('after_pullup')
            return

        # Common controls for all phases
        # Rotation depends on unstuck recency with vibration safety check
        last_unstuck = getattr(self.node.safety_manager, 'last_unstuck_time', 0.0)
        recency_limit = float(self.node.profile_manager.current_params.get('common', {}).get('unstuck_recency_limit', 3.0))
        rot_after = float(params.get('rotation_speed_after_unstuck_ctrl', 40.0))
        rot_norm = float(params.get('rotation_speed_normal_ctrl', 60.0))
        desired_rotation = rot_after if (self.node.get_time() - last_unstuck) < recency_limit else rot_norm
        self.node.set_safe_rotation(desired_rotation)
        self.node.ctrl_drill.feed_pressure = float(params.get('pullup_feed_pressure', 25.0))

        # Set control modes
        self.node.ctrl_drill.feed_speed_is_raw = False  # Normalized
        self.node.ctrl_drill.rotation_speed_is_raw = True  # Absolute RPM
        self.node.ctrl_drill.feed_pressure_is_raw = True  # Absolute pressure

        # Air and water controls
        # Turn off compressor near surface for first rod
        air_power = float(params.get('air_power_normal_ctrl', 1.0))
        if first_rod:
            current_from_ground = float(current_depth) - float(self.node.ground_spindle_depth)
            if current_from_ground < float(params.get('pullup_compressor_on_to_ground_limit', 0.4)):
                air_power = 0.0
        self.node.ctrl_air.power = air_power
        self.node.ctrl_water.ctrl = water_ctrl
        self.node.ctrl_dust_collector.value = bool(params.get('dust_ctrl', True))

        # Safety timeout
        max_pullup_time = float(params.get('max_pullup_time', 30.0))
        if self.node.get_time() - self.entry_time > max_pullup_time:
            self.node.log("Pullup timeout, transitioning to after_pullup")
            self.node.set_state('after_pullup')
            return


