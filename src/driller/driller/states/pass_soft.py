from base_node.base_fsm import BaseState


class PassSoftState(BaseState):
    """Pass soft state - drilling through soft formations.

    Key responsibilities:
    - Use reduced pressure for soft formations
    - Monitor for transition back to normal drilling
    - Handle stuck conditions
    - Manage water flow for soft formations
    """

    def __init__(self, node):
        super().__init__(name='pass_soft', node=node)
        self.entry_time = 0.0
        self.entry_spindle_depth = None

    def on_transition_to(self):
        """Actions on entering pass soft state"""
        self.entry_time = self.node.get_time()
        if self.node.subs.drill_state:
            self.entry_spindle_depth = self.node.subs.drill_state.head_pos

        self.node.log("Entering soft formation drilling mode")

    def do_work(self):
        """Main work cycle - drill through soft formation"""
        params = self.node.profile_manager.current_params.get('pass_soft', {})

        # Get current drill state
        ds = self.node.subs.drill_state
        act = self.node.current_action
        if not ds or not act:
            return

        # Check for stuck conditions
        if self.node.safety_manager.is_stuck:
            self.node.set_state('unstuck')
            return

        if self.node.safety_manager.is_hard_rot:
            self.node.set_state('hard_rot')
            return

        # Check if we've reached target depth
        if ds.head_pos >= float(act.drill_spindle_depth):
            self.node.set_state('wait_after_drill')
            return

        # Check for pullup needs
        if not self.node.safety_manager.check_air_system():
            self.node.set_state('pullup')
            self.node.pullup_action = 'air'
            return

        # Set controls for soft formation drilling with vibration safety
        self.node.ctrl_drill.feed_speed = float(params.get('feed_speed_ctrl', 0.7))
        desired_rotation = float(params.get('rotation_speed_ctrl', 90.0))
        self.node.set_safe_rotation(desired_rotation)

        # Additional controls
        self.node.ctrl_drill.feed_pressure = float(params.get('feed_pressure_ctrl', 30.0))
        self.node.ctrl_air.power = float(params.get('air_power_ctrl', 1.0))
        self.node.ctrl_dust_collector.value = bool(params.get('dust_ctrl', True))

        # Reduced pressure for soft formations
        base_pressure = float(params.get('base_feed_pressure', 25.0))

        # Apply depth-based pressure limits
        pressure_limit_table = params.get('feed_pressure_limit', [])
        if pressure_limit_table:
            max_pressure = self.node.profile_manager.get_value_for_current_depth(
                pressure_limit_table, default=base_pressure
            )
            feed_pressure = min(base_pressure, float(max_pressure))
        else:
            feed_pressure = base_pressure

        self.node.ctrl_drill.feed_pressure = feed_pressure

        # Set control modes
        self.node.ctrl_drill.feed_speed_is_raw = False  # Normalized
        self.node.ctrl_drill.rotation_speed_is_raw = True  # Absolute RPM
        self.node.ctrl_drill.feed_pressure_is_raw = True  # Absolute pressure

        # Water control from depth tables
        water_ctrl = self.node.profile_manager.get_value_for_current_depth(
            params.get('water_ctrl', []), default=0.2
        )
        self.node.ctrl_water.ctrl = float(water_ctrl)

        # Air control
        self.node.ctrl_air.power = float(params.get('air_power_ctrl', 0.9))

        # Check for transition back to normal drilling
        time_in_state = self.node.get_time() - self.entry_time
        min_soft_time = float(params.get('min_soft_drilling_time', 5.0))

        if time_in_state > min_soft_time:
            # Check drilling conditions to see if we should return to normal drilling
            current_pressure = float(ds.rot_pressure)
            normal_pressure_threshold = float(params.get('normal_pressure_threshold', 150.0))

            if current_pressure > normal_pressure_threshold:
                # Pressure increased, likely out of soft formation
                self.node.log("Pressure increased, returning to normal drilling")
                self.node.set_state('drilling')
                return

        # Safety check - maximum time in soft drilling
        max_soft_time = float(params.get('max_soft_drilling_time', 30.0))
        if time_in_state > max_soft_time:
            self.node.log("Maximum soft drilling time reached, returning to normal drilling")
            self.node.set_state('drilling')
            return


