from base_node.base_fsm import BaseState


class UnstuckState(BaseState):
    """Unstuck state - free drill from general stuck condition."""

    def __init__(self, node):
        super().__init__(name='unstuck', node=node)
        self.entry_time = 0.0
        self.entry_spindle_depth = None
        self.cycle_phase = 'up'  # 'up', 'down'
        self.cycle_count = 0
        self.phase_start_time = 0.0

    def on_transition_to(self):
        self.entry_time = self.node.get_time()
        if self.node.subs.drill_state:
            self.entry_spindle_depth = self.node.subs.drill_state.head_pos
        self.cycle_phase = 'up'
        self.cycle_count = 0
        self.phase_start_time = self.node.get_time()
        self.node.log(f"Starting unstuck cycles from depth {self.entry_spindle_depth:.3f}m")
        # Enable/disable check like ROS1
        common = self.node.profile_manager.current_params.get('common', {})
        enable_unstuck = bool(common.get('enable_unstuck', True))
        if not enable_unstuck:
            self.node.handle_error("Unstuck is disabled!", level=self.node.ERROR)

    def do_work(self):
        """Execute up-down cycles to free stuck drill"""
        params = self.node.profile_manager.current_params.get('unstuck', {})
        ds = self.node.subs.drill_state

        if not ds:
            return

        # Check if no longer stuck
        if not self.node.safety_manager.is_stuck:
            self.node.safety_manager.last_unstuck_time = self.node.get_time()
            self.node.log("Drill freed, returning to previous state")
            self.node.set_state('prev')  # Use BaseFSM functionality
            return

        current_time = self.node.get_time()
        phase_duration = current_time - self.phase_start_time

        up_time = float(params.get('up_phase_time', 2.0))
        down_time = float(params.get('down_phase_time', 1.5))

        if self.cycle_phase == 'up':
            # Up phase
            self.node.ctrl_drill.feed_speed = float(params.get('up_feed_speed', -0.9))
            self.node.ctrl_drill.rotation_speed = float(params.get('up_rotation_speed', 0.0))

            if phase_duration >= up_time:
                self.cycle_phase = 'down'
                self.phase_start_time = current_time

        elif self.cycle_phase == 'down':
            # Down phase
            self.node.ctrl_drill.feed_speed = float(params.get('down_feed_speed', 0.7))
            self.node.ctrl_drill.rotation_speed = float(params.get('down_rotation_speed', 30.0))

            if phase_duration >= down_time:
                self.cycle_phase = 'up'
                self.phase_start_time = current_time
                self.cycle_count += 1
                self.node.log(f"Unstuck cycle {self.cycle_count} completed")

        # Common controls
        self.node.ctrl_drill.feed_pressure = float(params.get('feed_pressure', 40.0))
        self.node.ctrl_air.power = float(params.get('air_power', 1.0))
        self.node.ctrl_water.ctrl = float(params.get('water_ctrl', 0.0))
        self.node.ctrl_dust_collector.value = bool(params.get('dust_ctrl', True))

        # Set control modes
        self.node.ctrl_drill.feed_speed_is_raw = False  # Normalized
        self.node.ctrl_drill.rotation_speed_is_raw = True  # Absolute RPM
        self.node.ctrl_drill.feed_pressure_is_raw = True  # Absolute pressure

        # Timeout check
        max_cycles = int(params.get('max_cycles', 5))
        max_time = float(params.get('max_unstuck_time', 30.0))

        if self.cycle_count >= max_cycles or (current_time - self.entry_time) > max_time:
            self.node.handle_error("Unstuck timeout - drill may be critically stuck",
                                 level=self.node.ERROR)
            return


