from base_node.base_fsm import BaseState


class AfterPullUpState(BaseState):
    """After pullup state - return to drilling position.

    Key responsibilities:
    - Return to drilling depth slowly
    - Detect ground contact again
    - Restore water flow gradually
    - Transition back to drilling when ready
    """

    def __init__(self, node):
        super().__init__(name='after_pullup', node=node)
        self.entry_time = 0.0
        self.entry_spindle_depth = None
        self.target_return_depth = None
        self.ground_detected = False

    def on_transition_to(self):
        """Actions on entering after pullup state"""
        self.entry_time = self.node.get_time()
        if self.node.subs.drill_state:
            self.entry_spindle_depth = self.node.subs.drill_state.head_pos
        # Reset ground detection timer and stop water like ROS1
        self._ground_detect_started = self.node.get_time()
        self.node.ctrl_water.ctrl = 0.0

        # Calculate target return depth near previous drilling point
        pullup_return_offset = 0.1
        if getattr(self.node, 'last_mandatory_pullup_spindle_depth', None):
            self.target_return_depth = float(self.node.last_mandatory_pullup_spindle_depth) - pullup_return_offset
        else:
            self.target_return_depth = float(self.node.ground_spindle_depth) + 0.2

        self.ground_detected = False
        self.node.log(f"Returning to drilling depth: {self.target_return_depth:.3f}m")

    def do_work(self):
        """Main work cycle - return to drilling position"""
        params = self.node.profile_manager.current_params.get('after_pullup', {})

        # Get current drill state
        ds = self.node.subs.drill_state
        if not ds:
            return

        current_depth = ds.head_pos
        current_speed = float(ds.head_speed)

        # Check if we've reached target depth
        if current_depth >= self.target_return_depth:
            # We're back at drilling depth
            self.node.log("Returned to drilling depth, resuming drilling")
            self.node.set_state('drilling')
            return

        # Ground detection and nominal air update like ROS1
        common = self.node.profile_manager.current_params.get('common', {})
        speed_thr_cmd = float(common.get('ground_detect_feed_speed_thr', 0.3))
        rot_pres_thr = float(params.get('ground_detect_rot_pres', 120.0))

        if current_speed > speed_thr_cmd or float(getattr(ds, 'rot_pressure', 0.0)) < rot_pres_thr:
            self._ground_detect_started = self.node.get_time()
            cur_air = float(getattr(ds, 'air_pressure', 0.0))
            max_air = float(common.get('max_air_pressure', 6.0))
            if (self.node.nominal_air_pres is None or float(self.node.nominal_air_pres) < cur_air) and cur_air < (max_air - 0.5):
                self.node.nominal_air_pres = cur_air

        # Set controls for slow return
        if self.ground_detected:
            # Slower descent after ground detection
            feed_speed = float(params.get('post_ground_feed_speed', 0.3))
            rotation_speed = float(params.get('post_ground_rotation_speed', 40.0))
            feed_pressure = float(params.get('post_ground_feed_pressure', 30.0))
        else:
            # Faster descent before ground
            feed_speed = float(params.get('pre_ground_feed_speed', 0.6))
            rotation_speed = float(params.get('pre_ground_rotation_speed', 20.0))
            feed_pressure = float(params.get('pre_ground_feed_pressure', 20.0))

        # Set controls with vibration safety for rotation
        self.node.ctrl_drill.feed_speed = feed_speed
        self.node.set_safe_rotation(rotation_speed)
        self.node.ctrl_drill.feed_pressure = feed_pressure

        # Air and dust collector controls
        self.node.ctrl_air.power = float(params.get('air_power_ctrl', 1.0))
        self.node.ctrl_dust_collector.value = bool(params.get('dust_ctrl', True))

        # Set control modes
        self.node.ctrl_drill.feed_speed_is_raw = False  # Normalized
        self.node.ctrl_drill.rotation_speed_is_raw = True  # Absolute RPM
        self.node.ctrl_drill.feed_pressure_is_raw = True  # Absolute pressure

        # Gradual water restoration
        time_in_state = self.node.get_time() - self.entry_time
        water_ramp_time = float(params.get('water_ramp_time', 3.0))
        max_water = float(getattr(self.node, 'saved_water_ctrl', 0.3))

        if time_in_state < water_ramp_time:
            # Gradual increase
            water_factor = time_in_state / water_ramp_time
            water_ctrl = max_water * water_factor
        else:
            water_ctrl = max_water

        self.node.ctrl_water.ctrl = water_ctrl

        # Air control
        self.node.ctrl_air.power = float(params.get('air_power_ctrl', 0.8))

        # Transition after sufficient ground detection time to soft pass
        detect_duration = float(common.get('ground_detect_duration', 1.0))
        if (self.node.get_time() - getattr(self, '_ground_detect_started', self.entry_time)) > detect_duration:
            if self.node.nominal_air_pres is not None:
                self.node.log(f"Nominal air pressure set to {float(self.node.nominal_air_pres):.2f} bar")
            # Restore saved water immediately and flag restored
            self.node.ctrl_water.ctrl = float(getattr(self.node, 'saved_water_ctrl', 0.3))
            self.node.saved_water_restored = True
            self.node.set_state('pass_soft')
            return


