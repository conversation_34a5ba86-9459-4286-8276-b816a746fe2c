from base_node.base_fsm import BaseState


class RaiseState(BaseState):
    """Raise state - drill raise after completion."""

    def __init__(self, node):
        super().__init__(name='raise', node=node)
        self.entry_time = 0.0
        self.entry_spindle_depth = None

    def on_transition_to(self):
        self.entry_time = self.node.get_time()
        if self.node.subs.drill_state:
            self.entry_spindle_depth = self.node.subs.drill_state.head_pos
        self.node.log(f"Starting drill raise from depth {self.entry_spindle_depth:.3f}m")

    def do_work(self):
        """Raise drill with variable speed and stops"""
        params = self.node.profile_manager.current_params.get('raise', {})
        common = self.node.profile_manager.current_params.get('common', {})
        ds = self.node.subs.drill_state
        act = self.node.current_action

        if not ds or not act:
            return

        # Check if we've reached raise target depth
        target_depth = act.raise_spindle_depth
        current_depth = ds.head_pos

        if current_depth <= target_depth:
            self.node.log(f"Raise complete at depth {current_depth:.3f}m")
            self.node.set_state('idle')
            return

        # Variable speed based on depth, with reduced speed zone like ROS1
        raise_depth_err = target_depth - current_depth
        reduced_zone = float(params.get('reduced_speed_zone', 0.5))
        if abs(raise_depth_err) > reduced_zone:
            raise_speed = -abs(float(params.get('max_raise_feed_speed', 0.8)))
        else:
            raise_speed = -abs(float(params.get('min_raise_feed_speed', 0.4)))

        # Stop air and rotation near surface
        stop_depth = float(params.get('stop_air_depth', 0.5))
        in_hole = self._is_in_hole(stop_depth)

        # Set controls for raising
        self.node.ctrl_drill.feed_speed = raise_speed
        if not in_hole:
            self.node.ctrl_drill.rotation_speed = 0.0
            self.node.ctrl_air.power = 0.0
            self.node.ctrl_dust_collector.value = False  # Like ROS1
        else:
            # Rotation depends on unstuck recency (direct control for raise operations)
            last_unstuck = self.node.safety_manager.last_unstuck_time
            recency_limit = float(common.get('unstuck_recency_limit', 3.0))
            rn = float(params.get('rotation_speed_normal_ctrl', 40.0))
            ra = float(params.get('rotation_speed_after_unstuck_ctrl', 20.0))
            self.node.ctrl_drill.rotation_speed = ra if (self.node.get_time() - last_unstuck) < recency_limit else rn
            self.node.ctrl_air.power = 1.0
            self.node.ctrl_dust_collector.value = bool(params.get('dust_ctrl', True))

        # Additional controls
        self.node.ctrl_drill.feed_pressure = float(params.get('feed_pressure_ctrl', 0.0))
        self.node.ctrl_drill.feed_pressure = float(params.get('raise_feed_pressure', 15.0))

        # Set control modes
        self.node.ctrl_drill.feed_speed_is_raw = False  # Normalized
        self.node.ctrl_drill.rotation_speed_is_raw = True  # Absolute RPM
        self.node.ctrl_drill.feed_pressure_is_raw = True  # Absolute pressure

        # Air already handled; water based on depth
        water_ctrl = self.node.profile_manager.get_value_for_current_depth(
            params.get('water_ctrl', []), default=0.3
        )
        self.node.ctrl_water.ctrl = float(water_ctrl)

    def _is_in_hole(self, max_distance: float) -> bool:
        ds = getattr(self.node.subs, 'drill_state', None)
        if ds is None:
            return False
        depth = float(getattr(ds, 'head_pos', 0.0))
        if depth < 0.1 + max_distance:
            return False
        if self.node.ground_spindle_depth is None:
            return False
        act = getattr(self.node, 'current_action', None)
        first_rod = bool(getattr(act, 'first_rod_flag', False)) if act else False
        if first_rod and (depth - float(self.node.ground_spindle_depth)) < max_distance:
            return False
        return True


