from base_node.base_fsm import BaseState


class HardRotState(BaseState):
    """Hard rotation state - free stuck rotation by lifting.

    Key responsibilities:
    - Lift drill to free stuck rotation
    - Stop rotation temporarily
    - Return to previous state when rotation is free
    - Handle timeout if rotation doesn't free
    """

    def __init__(self, node):
        super().__init__(name='hard_rot', node=node)
        self.entry_time = 0.0
        self.entry_spindle_depth = None
        self.previous_state = None

    def on_transition_to(self):
        """Actions on entering hard rotation state"""
        self.entry_time = self.node.get_time()
        if self.node.subs.drill_state:
            self.entry_spindle_depth = self.node.subs.drill_state.head_pos

        # Store previous state to return to (using BaseFSM functionality)
        if self.node._prev_state:
            self.previous_state = self.node._prev_state.name
        else:
            self.previous_state = 'drilling'  # Default fallback

        self.node.log(f"Hard rotation detected, lifting to free rotation (from {self.previous_state})")

    def do_work(self):
        """Main work cycle - lift to free rotation"""
        params = self.node.profile_manager.current_params.get('hard_rot', {})
        common = self.node.profile_manager.current_params.get('common', {})

        # Get current drill state
        ds = self.node.subs.drill_state
        if not ds:
            return

        # Set controls for lifting
        self.node.ctrl_drill.feed_speed = float(params.get('feed_speed_ctrl', -0.8))

        # Unstuck recency logic for rotation speed (direct control for unstuck operations)
        last_unstuck = getattr(self.node.safety_manager, 'last_unstuck_time', 0.0)
        recency_limit = float(common.get('unstuck_recency_limit', 3.0))
        if self.node.get_time() - last_unstuck < recency_limit:
            self.node.ctrl_drill.rotation_speed = float(params.get('rotation_speed_after_unstuck_ctrl', 0.0))
        else:
            self.node.ctrl_drill.rotation_speed = float(params.get('rotation_speed_normal_ctrl', 0.0))
        self.node.ctrl_drill.feed_pressure = float(params.get('feed_pressure_ctrl', 30.0))  # Low pressure

        # Set control modes
        self.node.ctrl_drill.feed_speed_is_raw = False  # Normalized
        self.node.ctrl_drill.rotation_speed_is_raw = True  # Absolute RPM
        self.node.ctrl_drill.feed_pressure_is_raw = True  # Absolute pressure

        # Air and water controls
        self.node.ctrl_air.power = float(params.get('air_power_ctrl', 1.0))  # Full air
        self.node.ctrl_water.ctrl = float(params.get('water_ctrl', 0.0))  # No water during lift
        self.node.ctrl_dust_collector.value = bool(params.get('dust_ctrl', True))

        # Complex exit conditions similar to ROS1
        time_in_state = self.node.get_time() - self.entry_time
        min_lift_time = float(params.get('min_lift_time', 1.0))

        depth_delta_ok = False
        rot_speed_ok = False
        rot_pressure_ok = False

        ds = self.node.subs.drill_state
        if ds is not None and self.entry_spindle_depth is not None:
            depth_delta_ok = (self.entry_spindle_depth - float(ds.head_pos)) >= float(params.get('depth_delta', 0.1))
            rot_speed_ok = float(getattr(ds, 'drill_rpm', 0.0)) > float(params.get('min_rotation_speed', 5.0))
            rot_pressure_ok = float(getattr(ds, 'rot_pressure', 0.0)) < float(params.get('max_rotation_pressure', 220.0))

        if time_in_state > min_lift_time and (depth_delta_ok or rot_speed_ok or rot_pressure_ok or (not self._is_in_hole())):
            prev = self.previous_state if self.previous_state in ['drilling', 'overburden_pass', 'pass_soft'] else 'drilling'
            self.node.set_state(prev)
            return

        # Timeout check - if we can't free rotation, go to unstuck
        max_lift_time = float(params.get('max_lift_time', 5.0))
        if time_in_state > max_lift_time:
            self.node.log("Hard rotation timeout, transitioning to unstuck")
            self.node.set_state('unstuck')
            return

        # Safety check - don't lift too high
        max_lift_distance = float(params.get('max_lift_distance', 0.5))
        if self.entry_spindle_depth is not None:
            lift_distance = self.entry_spindle_depth - ds.head_pos
            if lift_distance > max_lift_distance:
                self.node.log("Maximum lift distance reached, returning to drilling")
                self.node.set_state('drilling')
                return

    def _is_in_hole(self) -> bool:
        ds = getattr(self.node.subs, 'drill_state', None)
        if ds is None:
            return False
        if self.node.ground_spindle_depth is None:
            return False
        # Consider near surface as out of hole
        near_surface = float(ds.head_pos) < (0.1 + 0.0)
        first_rod = bool(getattr(self.node.current_action, 'first_rod_flag', False)) if self.node.current_action else False
        if first_rod and (float(ds.head_pos) - float(self.node.ground_spindle_depth)) < 0.0:
            return False
        return not near_surface


