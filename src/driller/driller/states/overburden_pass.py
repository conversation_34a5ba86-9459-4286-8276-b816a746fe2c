from base_node.base_fsm import BaseState


class OverburdenPassState(BaseState):
    """Overburden pass state - passing through upper soil layer.

    Key responsibilities:
    - Gradual spin-up of rotation speed
    - Air pressure calibration
    - Transition to main drilling when overburden passed
    - Handle stuck conditions
    """

    def __init__(self, node):
        super().__init__(name='overburden_pass', node=node)
        self.entry_time = 0.0
        self.entry_spindle_depth = None
        self.air_calibrated = False

    def on_transition_to(self):
        """Actions on entering overburden pass state"""
        self.entry_time = self.node.get_time()
        if self.node.subs.drill_state:
            self.entry_spindle_depth = self.node.subs.drill_state.head_pos
        self.air_calibrated = False

    def do_work(self):
        """Main work cycle - gradual spin-up and overburden passing"""
        params = self.node.profile_manager.current_params.get('overburden_pass', {})

        # Get current drill state
        ds = self.node.subs.drill_state
        if not ds:
            return

        # Check for stuck conditions
        if self.node.safety_manager.is_stuck:
            self.node.set_state('unstuck')
            return

        if self.node.safety_manager.is_hard_rot:
            self.node.set_state('hard_rot')
            return

        # Air pressure calibration (once per state entry)
        if not self.air_calibrated:
            calibration_time = float(params.get('air_calibration_time', 2.0))
            if self.node.get_time() - self.entry_time > calibration_time:
                if ds.air_pressure > 0.5:  # Valid air pressure reading
                    self.node.nominal_air_pres = float(ds.air_pressure)
                    self.node.log(f"Air pressure calibrated: {self.node.nominal_air_pres:.2f} bar")
                self.air_calibrated = True

        # Gradual rotation spin-up
        time_in_state = self.node.get_time() - self.entry_time
        spinup_time = float(params.get('rotation_spinup_time', 3.0))
        max_rotation = float(params.get('max_rotation_speed', 80.0))

        if time_in_state < spinup_time:
            # Gradual increase
            rotation_factor = time_in_state / spinup_time
            current_rotation = max_rotation * rotation_factor
        else:
            current_rotation = max_rotation

        # Set controls with vibration safety check
        self.node.ctrl_drill.feed_speed = float(params.get('feed_speed_ctrl', 0.8))
        self.node.set_safe_rotation(current_rotation)

        # Additional controls
        self.node.ctrl_drill.feed_pressure = float(params.get('feed_pressure_ctrl', 30.0))
        self.node.ctrl_air.power = float(params.get('air_power_ctrl', 1.0))
        self.node.ctrl_dust_collector.value = bool(params.get('dust_ctrl', True))
        self.node.ctrl_drill.feed_pressure = float(params.get('feed_pressure_ctrl', 40.0))

        # Set control modes
        self.node.ctrl_drill.feed_speed_is_raw = False  # Normalized
        self.node.ctrl_drill.rotation_speed_is_raw = True  # Absolute RPM
        self.node.ctrl_drill.feed_pressure_is_raw = True  # Absolute pressure

        # Air and water
        self.node.ctrl_air.power = float(params.get('air_power_ctrl', 0.8))
        water_ctrl = self.node.profile_manager.get_value_for_current_depth(
            params.get('water_ctrl', []), default=0.2
        )
        self.node.ctrl_water.ctrl = float(water_ctrl)

        # Check transition to drilling
        overburden_depth = float(params.get('overburden_depth', 1.5))
        if self.entry_spindle_depth is not None:
            depth_drilled = float(ds.head_pos) - self.entry_spindle_depth
            if depth_drilled >= overburden_depth:
                self.node.log("Overburden passed, transitioning to drilling")
                self.node.set_state('drilling')
                return

        # Safety check - maximum overburden depth
        max_overburden = float(params.get('max_overburden_depth', 3.0))
        if self.entry_spindle_depth is not None:
            depth_drilled = float(ds.head_pos) - self.entry_spindle_depth
            if depth_drilled >= max_overburden:
                self.node.log("Maximum overburden depth reached, transitioning to drilling")
                self.node.set_state('drilling')
                return


