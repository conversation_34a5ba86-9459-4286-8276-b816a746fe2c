from __future__ import annotations

from copy import deepcopy
from typing import Any, Dict, List, Optional


class ProfileManager:
    """Profiles + depth tables logic.

    Keeps current merged params in `current_params`.
    """

    def __init__(self, node: 'Driller') -> None:
        self.node = node
        self._profiles_cfg: Dict[str, Any] = {}
        self.current_profiles: List[str] = []
        self.current_params: Dict[str, Any] = {}
        self._load_profiles()
        self._apply_profiles([])

    def _load_profiles(self) -> None:
        """Load drilling profiles configuration with validation."""
        try:
            self._profiles_cfg = self.node._query_params('Driller')  # type: ignore[attr-defined]
            self._validate_configuration()
        except Exception as e:
            self.node.log(f"Failed to load drilling profiles: {e}", level=self.node.ERROR)
            self._profiles_cfg = {}

    def _validate_configuration(self) -> None:
        """Validate basic configuration structure."""
        if not isinstance(self._profiles_cfg, dict):
            raise ValueError("Profiles configuration must be a dictionary")

        if 'default' not in self._profiles_cfg:
            raise ValueError("Missing 'default' section in profiles configuration")

        if 'profiles' not in self._profiles_cfg:
            self.node.log("No 'profiles' section found in configuration", level=self.node.WARN)

        # Validate short hole threshold exists
        try:
            short_threshold = self._profiles_cfg['default']['common']['short_hole_max_depth']
            self.node.log(f"Short hole threshold: {short_threshold}m")
        except KeyError:
            self.node.log("Missing short_hole_max_depth in default.common configuration", level=self.node.WARN)

    def update_profiles(self) -> None:
        """Update active profiles based on current drilling conditions."""
        new_profiles = self._get_active_profiles()
        if new_profiles != self.current_profiles:
            self.node.log(f"Applying changed profiles: {new_profiles} (was: {self.current_profiles})")
            if self._apply_profiles(new_profiles):
                self.current_profiles = new_profiles
                self.node.log("Profile application finished")
            else:
                self.node.log("Profile application failed", level=self.node.ERROR)

    def _get_active_profiles(self) -> List[str]:
        """Calculate required profiles based on rock type, water conditions, and hole depth."""
        profiles: List[str] = []
        rock = getattr(self.node.subs, 'rock_type', None)
        if rock is not None:
            if getattr(rock, 'type', 0) == 1:
                profiles.append('hard')
            if getattr(rock, 'type', 0) == 2:
                profiles.append('cracked')
            if getattr(rock, 'is_flooded', False):
                profiles.append('wet')
        if self._is_short_hole():
            profiles = ['default-short'] + [
                p + '-short' if self._has_profile(p + '-short') else p for p in profiles
            ]
        return profiles

    def _apply_profiles(self, profiles: List[str]) -> bool:
        base = deepcopy(self._profiles_cfg.get('default', {}))
        for p in profiles:
            if self._has_profile(p):
                profile_params = self._profiles_cfg['profiles'][p]
                # Validate profile parameters against defaults (like ROS1)
                self._validate_profile_params(base, profile_params, p)
                base = _deep_update(base, profile_params)
                self.node.log(f"Applied profile '{p}'")
            else:
                self.node.log(f"No profile '{p}' in config", level=self.node.WARN)
                return False
        self.current_params = base
        return True

    def _has_profile(self, name: str) -> bool:
        return 'profiles' in self._profiles_cfg and name in self._profiles_cfg['profiles']

    def _validate_profile_params(self, base_dict: Dict[str, Any], profile_dict: Dict[str, Any],
                                profile_name: str, prefix: str = "") -> None:
        """Recursively validate profile parameters against defaults configuration."""
        for key, value in profile_dict.items():
            if key not in base_dict:
                self.node.log(f"Warning: Parameter '{prefix}{key}' from profile '{profile_name}' does not exist in defaults.",
                             level=self.node.WARN)
            elif isinstance(value, dict) and isinstance(base_dict.get(key), dict):
                # Recursively check nested dictionaries
                self._validate_profile_params(base_dict[key], value, profile_name, f"{prefix}{key}.")

    def _is_short_hole(self) -> bool:
        try:
            max_depth = float(self._profiles_cfg['default']['common']['short_hole_max_depth'])
        except Exception:
            return False
        act = getattr(self.node, 'current_action', None)
        if act is None:
            return False
        return float(getattr(act, 'hole_depth', 0.0)) < max_depth

    def get_value_for_current_depth(self, table: List[Dict[str, Any]], default: Any = None) -> Any:
        if not table or getattr(self.node.subs, 'drill_state', None) is None:
            return default
        adjusted: List[Dict[str, Any]] = []
        for entry in table:
            depth_ref = entry.get('depth', {})
            th = self._convert_depth_reference(depth_ref)
            if th is not None:
                adjusted.append({'threshold': th, 'value': entry.get('value')})
            # Note: Detailed warnings are now handled in _convert_depth_reference
        if not adjusted:
            return default
        adjusted.sort(key=lambda x: x['threshold'])
        cur_depth = float(self.node.subs.drill_state.head_pos)
        # Warn if current depth is less than the lowest threshold and no explicit default provided
        if default is None and cur_depth < adjusted[0]['threshold']:
            self.node.log(
                f"Current spindle depth {cur_depth:.3f}m is less than the lowest threshold {adjusted[0]['threshold']:.3f}m in depth table. "
                "Verify settings in the profile. Using first value from table.",
                level=self.node.WARN,
                period=3.0
            )
        result = default if default is not None else adjusted[0]['value']
        for e in adjusted:
            if cur_depth >= e['threshold']:
                result = e['value']
            else:
                break
        return result

    def _convert_depth_reference(self, ref: Dict[str, float]) -> Optional[float]:
        """Convert depth reference to absolute spindle depth with validation."""
        act = getattr(self.node, 'current_action', None)
        if act is None:
            return None

        # from_ground reference
        if 'from_ground' in ref:
            if getattr(act, 'first_rod_flag', False):
                return float(self.node.ground_spindle_depth) + float(ref['from_ground'])
            else:
                self.node.log("from_ground depth reference is not applicable for added shafts",
                             level=self.node.WARN, period=3.0)
                return None

        # from_added_shaft_start reference
        if 'from_added_shaft_start' in ref:
            if not getattr(act, 'first_rod_flag', False):
                return float(self.node.action_start_spindle_depth) + float(ref['from_added_shaft_start'])
            else:
                self.node.log("from_added_shaft_start depth reference is not applicable for first shaft",
                             level=self.node.WARN, period=3.0)
                return None

        # from_shaft_finish reference
        if 'from_shaft_finish' in ref:
            if not getattr(act, 'last_rod_flag', False):
                return float(act.drill_spindle_depth) - float(ref['from_shaft_finish'])
            else:
                self.node.log("from_shaft_finish depth reference is not applicable for last shaft",
                             level=self.node.WARN, period=3.0)
                return None

        # from_hole_finish reference
        if 'from_hole_finish' in ref:
            if getattr(act, 'last_rod_flag', False):
                return float(act.drill_spindle_depth) - float(ref['from_hole_finish'])
            else:
                self.node.log("from_hole_finish depth reference is only applicable for last shaft",
                             level=self.node.WARN, period=3.0)
                return None

        # drilled reference
        if 'drilled' in ref:
            state = self.node.current_state
            if state and hasattr(state, 'entry_spindle_depth') and self.node.subs.drill_state:
                return float(state.entry_spindle_depth) + float(ref['drilled'])
            else:
                self.node.log("Depth reference 'drilled' used without entry depth context; skipping.",
                             level=self.node.WARN, period=3.0)
                return None

        # Unknown or empty reference
        if ref:
            self.node.log(f"Unsupported depth reference spec: {ref}", level=self.node.WARN, period=3.0)
        return None


def _deep_update(dst: Dict[str, Any], src: Dict[str, Any]) -> Dict[str, Any]:
    for k, v in src.items():
        if isinstance(v, dict):
            dst[k] = _deep_update(dst.get(k, {}), v)
        else:
            dst[k] = v
    return dst


