from __future__ import annotations

from typing import Optional


class SafetyManager:
    """Central safety logic: depth validation, stuck detection, air checks, vibration monitoring, arm management."""

    def __init__(self, node: 'Driller') -> None:
        self.node = node
        self.last_valid_depth_time: float = node.get_time()
        self.drill_speed_smoothed: Optional[float] = None

        # Stuck detection
        self.is_stuck: bool = False
        self.is_hard_rot: bool = False
        self.last_normal_rotation_time: float = node.get_time()
        self.last_nostuck_move_time: float = node.get_time()
        self.last_normal_rp_time: float = node.get_time()
        self.last_unstuck_time: float = 0.0

        # Air monitoring
        self.last_air_nominal_time: float = node.get_time()
        self.air_bad_start_time: Optional[float] = None
        self.last_air_check_time: float = node.get_time()
        self.first_air_is_bad_time: float = 0.0

        # Vibration monitoring
        self.last_high_vibration_time: float = 0.0

        # Arm management
        self.sent_arm_open: bool = False
        self.sent_arm_close: bool = False
        self.sent_arm_time: float = 0.0

        # Hardware configuration flags
        self.arm_present: bool = True  # Will be loaded from config
        self.use_rotation_speed_sensor: bool = True  # Will be loaded from config

        # Load hardware configuration
        self._load_hardware_config()

    def _load_hardware_config(self) -> None:
        """Load hardware configuration flags from system parameters."""
        try:
            # Try to get hardware flags from global params or node params
            if hasattr(self.node, 'global_params') and 'system_flags' in self.node.global_params:
                flags = self.node.global_params['system_flags']
                self.arm_present = flags.get('arm_present', True)
                self.use_rotation_speed_sensor = flags.get('use_rotation_speed_sensor', True)
            else:
                # Fallback to node params or defaults
                self.arm_present = getattr(self.node, 'arm_present', True)
                self.use_rotation_speed_sensor = getattr(self.node, 'use_rotation_speed_sensor', True)

            self.node.log(f"Hardware config: arm_present={self.arm_present}, use_rotation_speed_sensor={self.use_rotation_speed_sensor}")
        except Exception as e:
            self.node.log(f"Failed to load hardware config, using defaults: {e}", level=self.node.WARN)
            self.arm_present = True
            self.use_rotation_speed_sensor = True

    # ------------------------------------------------------------------
    # Depth validation + speed smoothing
    # ------------------------------------------------------------------
    def validate_depth_data(self) -> bool:
        ds = getattr(self.node.subs, 'drill_state', None)
        if ds is None:
            return False
        if getattr(ds, 'head_pos_is_reliable', False):
            self.last_valid_depth_time = self.node.get_time()
            cur = float(getattr(ds, 'head_speed', 0.0))
            if self.drill_speed_smoothed is None:
                self.drill_speed_smoothed = cur
            else:
                self.drill_speed_smoothed += (cur - self.drill_speed_smoothed) * 0.1
            return True
        # timeouts per state
        timeout = 0.6
        st = self.node.current_state.name if self.node.current_state else ''
        if st in ['idle', 'drilling', 'after_pullup', 'pass_soft']:
            timeout = 8.0
        return (self.node.get_time() - self.last_valid_depth_time) <= timeout

    # ------------------------------------------------------------------
    # Stuck detection
    # ------------------------------------------------------------------
    def update_stuck_detection(self) -> None:
        """Update stuck detection using configuration parameters and sensor data."""
        node = self.node
        ds = node.subs.drill_state
        if ds is None:
            self.is_stuck = False
            self.is_hard_rot = False
            return

        # Get configuration parameters
        common_params = node.profile_manager.current_params.get('common', {})
        now = node.get_time()

        # Reset stuck detection when not allowed to move or not in hole
        if (not node.permission) or (not node.robomode) or not self._is_in_hole(0.1):
            self._reset_stuck(now)
            return

        # Check rotation stuck condition
        rotation_cmd = abs(node.ctrl_drill.rotation_speed) > 0.95
        too_low_rotation_speed = float(common_params.get('too_low_rotation_speed', 3.0))
        rotation_low = abs(ds.drill_rpm) < too_low_rotation_speed
        stuck_criteria_rot = (self.use_rotation_speed_sensor and rotation_cmd and rotation_low)
        if not stuck_criteria_rot:
            self.last_normal_rotation_time = now

        # Check movement stuck condition
        move_up_cmd = node.ctrl_drill.feed_speed < -0.95
        stuck_speed_threshold = float(common_params.get('stuck_speed_threshold', 0.05))
        move_up_slow = ds.head_speed > -stuck_speed_threshold
        stuck_criteria_move = move_up_cmd and move_up_slow
        if not stuck_criteria_move:
            self.last_nostuck_move_time = now

        # Check for high rotation pressure
        too_high_rp_threshold = float(common_params.get('too_high_rp_threshold', 310.0))
        high_rp = ds.rot_pressure > too_high_rp_threshold
        if not high_rp:
            self.last_normal_rp_time = now

        # Detect hard rotation conditions
        no_rotation_duration = float(common_params.get('no_rotation_duration', 0.5))
        high_rp_duration = float(common_params.get('high_rp_duration', 1.5))
        has_long_no_rotation = (now - self.last_normal_rotation_time) >= no_rotation_duration
        has_long_high_rotation_pressure = (now - self.last_normal_rp_time) > high_rp_duration
        self.is_hard_rot = has_long_no_rotation or has_long_high_rotation_pressure

        # Detect general stuck condition
        state_duration = node.get_current_state_duration()
        time_since_unstuck = now - self.last_unstuck_time
        min_state_duration_to_stuck = float(common_params.get('min_state_duration_to_stuck', 0.391))
        ignore_stuck_duration = float(common_params.get('ignore_stuck_duration', 3.0))
        stuck_move_duration = float(common_params.get('stuck_move_duration', 1.247))

        allow_stuck_detection = (state_duration > min_state_duration_to_stuck and
                                time_since_unstuck > ignore_stuck_duration)
        has_long_no_move = (now - self.last_nostuck_move_time) > stuck_move_duration

        if allow_stuck_detection and (has_long_no_move or self.is_hard_rot):
            self.is_stuck = True
        else:
            self.is_stuck = False

    def _reset_stuck(self, now: float) -> None:
        self.is_stuck = False
        self.is_hard_rot = False
        self.last_normal_rotation_time = now
        self.last_nostuck_move_time = now

    def _is_in_hole(self, max_distance: float = 0.0) -> bool:
        """Check if drill is in hole based on spindle depth."""
        ds = self.node.subs.drill_state
        if ds is None:
            return False
        spindle_depth = ds.head_pos
        return spindle_depth >= 0.1 + max_distance

    # ------------------------------------------------------------------
    # Air / compressor checks
    # ------------------------------------------------------------------
    def check_air_is_nominal(self) -> bool:
        """Check if air pressure is within nominal limits."""
        ds = self.node.subs.drill_state
        if ds is None:
            return True

        common_params = self.node.profile_manager.current_params.get('common', {})
        cur_p = ds.air_pressure
        max_air_pressure = float(common_params.get('max_air_pressure', 5.5))
        max_air_pressure_excess = float(common_params.get('max_air_pressure_excess', 0.6))

        if cur_p > max_air_pressure:
            return False
        if (self.node.nominal_air_pres is not None and
            cur_p > float(self.node.nominal_air_pres) + max_air_pressure_excess):
            return False
        return True

    def check_air_system(self) -> bool:
        """Check air system and return False if pullup needed due to high pressure."""
        now = self.node.get_time()

        # Check if air is nominal
        if self.check_air_is_nominal():
            self.last_air_nominal_time = now
        else:
            common_params = self.node.profile_manager.current_params.get('common', {})
            air_not_nominal_max_time = float(common_params.get('air_not_nominal_max_time', 2.0))
            if now - self.last_air_nominal_time > air_not_nominal_max_time:
                self.node.log("Too high air pressure! Need to pullup", level=self.node.WARN)
                return False  # Signal pullup needed
        return True

    def check_air_compressor(self) -> None:
        """Check air compressor functionality and detect malfunctions."""
        ds = self.node.subs.drill_state
        if ds is None:
            return

        now = self.node.get_time()
        cur_p = ds.air_pressure

        # Compressor diagnostic
        compressor_enabled = (self.node.ctrl_air.power > 0) and self.node.permission
        air_is_ok = ((compressor_enabled and cur_p > 0.7) or
                     (not compressor_enabled and cur_p < 0.3))

        if air_is_ok:
            self.first_air_is_bad_time = 0.0
        else:
            # If this is the first instance of bad air pressure
            if not self.first_air_is_bad_time:
                self.first_air_is_bad_time = now
            # If we missed an air check cycle
            elif now - self.last_air_check_time > 2 * (1.0 / 10.0):  # Assuming 10Hz rate
                self.first_air_is_bad_time = min(self.first_air_is_bad_time, now)
            # If bad air pressure persists beyond acceptable duration
            else:
                common_params = self.node.profile_manager.current_params.get('common', {})
                air_transient_response_time = float(common_params.get('air_transient_response_time', 7.0))
                if now - self.first_air_is_bad_time > air_transient_response_time:
                    self.node.handle_error("Compressor malfunction!", level=self.node.ERROR)

        self.last_air_check_time = now

    # ------------------------------------------------------------------
    # Vibration monitoring
    # ------------------------------------------------------------------
    def check_vibration(self) -> bool:
        """Check for high vibrations and return True if vibrations exceed configured limits."""
        common_params = self.node.profile_manager.current_params.get('common', {})

        # Check if vibration monitoring is enabled
        if not common_params.get('check_vibrations', True):
            return False

        # Get vibration data
        vibration_state = self.node.subs.vibration_state
        if vibration_state is None:
            return False

        vibration_limits = common_params.get('vibration_limits', [])
        if not vibration_limits:
            return False

        too_high_vibration = False
        vibrations = vibration_state.vibrations

        for v in vibrations:
            v_frequency = v.frequency
            v_amplitude = v.amplitude

            for i in range(len(vibration_limits) - 1):
                limit_freq = float(vibration_limits[i].get('frequency', 0.0))
                limit_amp = float(vibration_limits[i].get('amplitude', 90.0))
                next_limit_freq = float(vibration_limits[i + 1].get('frequency', 999.0))

                if (limit_freq < v_frequency < next_limit_freq and v_amplitude > limit_amp):
                    too_high_vibration = True
                    break

            if too_high_vibration:
                break

        if too_high_vibration:
            now = self.node.get_time()
            high_vibration_duration_threshold = float(common_params.get('high_vibration_duration_threshold', 2.0))
            if now - self.last_high_vibration_time > high_vibration_duration_threshold:
                self.node.log("Detected high vibrations!", level=self.node.WARN)
            self.last_high_vibration_time = now
            return True

        return False

    def set_safe_rotation(self, value: Optional[float] = None) -> float:
        """Set rotation speed with vibration safety check and automatic speed reduction."""
        common_params = self.node.profile_manager.current_params.get('common', {})
        max_speed = float(common_params.get('max_rotation_speed', 115.0))
        reduced_speed = float(common_params.get('reduced_rotation_speed', 65.0))

        # Ensure reduced rotation is less than max rotation speed
        if reduced_speed > max_speed:
            reduced_speed = max_speed
            self.node.log("Configuration error: Reduced rotation speed should be less than max rotation speed.",
                         level=self.node.WARN)

        if value is None or value > max_speed:
            value = max_speed

        # Check vibrations
        self.check_vibration()

        # Apply vibration-based speed reduction
        now = self.node.get_time()
        high_vibration_duration_threshold = float(common_params.get('high_vibration_duration_threshold', 2.0))
        time_since_high_vibration = now - self.last_high_vibration_time
        is_low_vibration = time_since_high_vibration > high_vibration_duration_threshold

        final_speed = value if is_low_vibration else reduced_speed
        return final_speed

    # ------------------------------------------------------------------
    # Arm management
    # ------------------------------------------------------------------
    def manage_arm(self) -> bool:
        """Manage arm open/close commands and return True if string movement should be denied."""
        if not self.arm_present:
            return False

        common_params = self.node.profile_manager.current_params.get('common', {})
        current_state_name = self.node.current_state.name if self.node.current_state else 'idle'

        # Return value which tells if string movement should be denied
        deny_movement = False

        # Get drill state
        ds = self.node.subs.drill_state
        if ds is None:
            return False

        spindle_depth = ds.head_pos

        # Reset arm state flags based on arm status
        arm_status = self.node.subs.arm_status
        if arm_status is not None:
            status = arm_status.status
            if status == 'CLOSED':  # Assuming string constants
                self.sent_arm_close = False
            elif status == 'OPEN':
                self.sent_arm_open = False

        # Arm open/close delays
        now = self.node.get_time()
        time_since_last_arm_action = now - self.sent_arm_time
        if self.sent_arm_open and time_since_last_arm_action > 5.0:
            self.sent_arm_open = False
        if self.sent_arm_close and time_since_last_arm_action > 5.0:
            self.sent_arm_close = False

        # Check if not in IDLE or remote
        if current_state_name != 'idle' and not self._is_remote_mode():
            hole_angle = getattr(self.node, 'hole_angle', 0)
            need_to_close_arm = (common_params.get('close_arm', True) and current_state_name == 'raise') or hole_angle != 0

            # Get arm control depths
            arm_close_depth = float(common_params.get('arm_close_depth', 5.0))
            arm_open_depth = float(common_params.get('arm_open_depth', 7.0))
            arm_close_min_depth = float(common_params.get('arm_close_min_depth', 4.5))
            arm_open_max_depth = float(common_params.get('arm_open_max_depth', 7.5))

            # Decide on arm action based on depth and other conditions
            if spindle_depth < arm_close_depth and not self.sent_arm_close:
                if need_to_close_arm and self.arm_present and arm_status and arm_status.status != 'CLOSED':
                    self._close_arm()
            elif spindle_depth > arm_open_depth and not self.sent_arm_open:
                if arm_status and arm_status.status != 'OPEN':
                    self._open_arm()

            # Conditions to deny string movement
            if ((spindle_depth < arm_close_min_depth and arm_status.status != 'CLOSED' and need_to_close_arm) or
                (spindle_depth > arm_open_max_depth and arm_status.status != 'OPEN')):
                deny_movement = True
                self.node.log("Feed locked while rod support in wrong state", level=self.node.WARN, period=2.0)

        return deny_movement

    def _is_remote_mode(self) -> bool:
        """Check if system is in remote control mode."""
        main_mode = self.node.subs.main_mode
        if main_mode is None:
            return False
        return 'remote' in main_mode.mode.lower()

    def _open_arm(self) -> None:
        """Send arm open command."""
        self._publish_arm_action(do_open=True)
        self.sent_arm_open = True

    def _close_arm(self) -> None:
        """Send arm close command."""
        self._publish_arm_action(do_open=False)
        self.sent_arm_close = True

    def _publish_arm_action(self, do_open: bool = False) -> None:
        """Publish arm action command to control arm position."""
        try:
            # Create arm action message
            if hasattr(self.node, 'pub_arm'):
                from drill_msgs.msg import OpenCloseAction
                message = OpenCloseAction()
                message.header.stamp = self.node.get_rostime()
                message.open = do_open
                self.node.pub_arm.publish(message)
                self.sent_arm_time = self.node.get_time()
                self.node.log(f"Arm {'open' if do_open else 'close'} command sent")
            else:
                self.node.log("Arm publisher not available", level=self.node.WARN)
        except Exception as e:
            self.node.log(f"Failed to publish arm action: {e}", level=self.node.ERROR)

    # ------------------------------------------------------------------
    # Comprehensive safety check
    # ------------------------------------------------------------------
    def check_all_safety_systems(self) -> bool:
        """Comprehensive safety check combining all safety systems."""
        current_state_name = self.node.current_state.name if self.node.current_state else 'idle'

        # Check if need to update profiles
        if current_state_name != 'idle':
            self.node.profile_manager.update_profiles()

        # Check depth data
        if not self.validate_depth_data():
            return False

        # Update stuck detection
        self.update_stuck_detection()

        # Check main mode
        main_mode = getattr(self.node.subs, 'main_mode', None)
        if main_mode is None:
            return False

        # Check if we're in IDLE state or remote modes
        if current_state_name == 'idle' or self._is_remote_mode():
            return True

        # Check air compressor (only when not in idle and not remote)
        if self.node.permission and self.node.robomode:
            self.check_air_compressor()

        return True



