from __future__ import annotations

import rclpy
from typing import Optional
from base_node.base_fsm import BaseFSM
from drill_msgs.msg import DrillCtrl, FloatCtrl, AirCtrl, DrillerAction, DrillState, OpenCloseAction, RockType, VibrationArray, StateMachineStatus
from common_msgs.msg import VehicleBehaviorMode, BoolStamped
from std_srvs.srv import Trigger

from .profile_manager import ProfileManager
from .safety_manager import SafetyManager
from .states import (
    IdleState, TouchdownState, OverburdenPassState, DrillingState, HardRotState,
    PullUpState, AfterPullUpState, PassSoftState, WaitAfterDrillState, RaiseState, UnstuckState,
)


class Driller(BaseFSM):
    def __init__(self) -> None:
        super().__init__('driller')

        self.ctrl_drill = DrillCtrl()
        self.ctrl_air = AirCtrl()
        self.ctrl_water = FloatCtrl()
        self.ctrl_dust_collector = BoolStamped()
        self.current_action = None
        self.ground_spindle_depth = 0.0
        self.action_start_spindle_depth = 0.0
        # Water control save/restore across pullups
        self.saved_water_ctrl = 0.0
        self.saved_water_restored = True

        # Counters and flags for drilling logic
        self.short_pullup_cnt = 0
        self.last_mandatory_pullup_spindle_depth = None
        self.pullup_action = None  # Type of pullup: 'short', 'long', 'cracked', 'air'

        # Additional attributes for safety systems
        self.hole_angle = 0  # Hole angle for arm management
        self.nominal_air_pres = None  # Nominal air pressure

        t = self.node_params.get('timeouts', {}) if isinstance(self.node_params, dict) else {}
        self.add_subscriber('/driller_action', DrillerAction, 'driller_action', t.get('driller_action', 1.0))
        self.add_subscriber('/drill_state', DrillState, 'drill_state', t.get('drill_state', 0.5))
        self.add_subscriber('/rock_type', RockType, 'rock_type', t.get('rock_type', 5.0))

        # Additional safety-related subscribers
        self.add_subscriber('/vibration_state', VibrationArray, 'vibration_state', t.get('vibration_state', 1.0))
        self.add_subscriber('/arm_controller_status', StateMachineStatus, 'arm_status', t.get('arm_status', 1.0))
        self.add_subscriber('/main_mode', VehicleBehaviorMode, 'main_mode', t.get('main_mode', 1.0))

        self.pub_drill = self.create_publisher(DrillCtrl, '/driller_setpoints', 10)
        self.pub_water = self.create_publisher(FloatCtrl, '/water_ctrl_auto', 10)
        self.pub_air = self.create_publisher(AirCtrl, '/air_ctrl', 10)
        self.pub_arm = self.create_publisher(OpenCloseAction, '/arm_action', 10)
        self.pub_dust_collector = self.create_publisher(BoolStamped, '/dust_collector_ctrl', 10)

        self.nominal_air_pres = None
        self.create_service(Trigger, '/driller/recalibrate_air', self._srv_recalibrate_air)

        self.profile_manager = ProfileManager(self)
        self.safety_manager = SafetyManager(self)

        self.add_states(
            IdleState(self), TouchdownState(self), OverburdenPassState(self), DrillingState(self),
            HardRotState(self), PullUpState(self), AfterPullUpState(self), PassSoftState(self),
            WaitAfterDrillState(self), RaiseState(self), UnstuckState(self),
        )
        self.set_state('idle')

    def _srv_recalibrate_air(self, req, resp):
        if self.subs.drill_state is None:
            resp.success = False
            resp.message = 'No drill_state'
            return resp
        self.nominal_air_pres = float(getattr(self.subs.drill_state, 'air_pressure', 0.0))
        resp.success = True
        resp.message = f'nominal_air_pres={self.nominal_air_pres:.3f}'
        return resp

    def stop_control(self) -> None:
        self.ctrl_drill.feed_speed = 0.0
        self.ctrl_drill.rotation_speed = 0.0
        self.ctrl_drill.feed_pressure = 0.0
        self.ctrl_drill.feed_speed_is_raw = True
        self.ctrl_drill.rotation_speed_is_raw = True
        self.ctrl_drill.feed_pressure_is_raw = True
        self.ctrl_air.power = 0.0
        self.ctrl_air.enabled = False
        self.ctrl_water.ctrl = 0.0
        self.ctrl_dust_collector.value = False
        self._publish_controls()

    def safety_check(self) -> bool:
        # Validate depth data
        if not self.safety_manager.validate_depth_data():
            self.handle_error("Invalid depth data", level=self.ERROR)
            return False
        # Update stuck
        self.safety_manager.update_stuck_detection()
        # Air system basic check – return False to trigger pullup in states
        self.safety_manager.check_air_system()
        return True

    def do_work_finally(self) -> None:
        # Handle new driller actions
        if self.subs.driller_action and (
            self.cur_action_id != getattr(self.subs.driller_action, 'id', -1)
        ):
            self.current_action = self.subs.driller_action
            self.last_action_id = self.cur_action_id
            self.cur_action_id = getattr(self.current_action, 'id', -1)
            if self.subs.drill_state:
                self.action_start_spindle_depth = float(self.subs.drill_state.head_pos)

        # Comprehensive safety checks
        if not self.safety_manager.check_all_safety_systems():
            self.log("Safety check failed, stopping operations", level=self.ERROR)
            self.stop_control()
            return

        # Update profiles based on current conditions
        self.profile_manager.update_profiles()

        # Manage arm and get movement denial status
        deny_string_movement = self.safety_manager.manage_arm()

        # Publish controls with safety overrides
        self._publish_controls(deny_string_movement)

    def _publish_controls(self, deny_string_movement: bool = False) -> None:
        """Publish control commands with safety overrides for feed movement."""
        # Apply safety override for feed movement
        original_feed_speed = self.ctrl_drill.feed_speed
        if deny_string_movement:
            self.ctrl_drill.feed_speed = 0.0

        # Publish drill controls
        self.ctrl_drill.header.stamp = self.get_rostime()
        self.pub_drill.publish(self.ctrl_drill)

        # Restore original feed speed for internal state
        if deny_string_movement:
            self.ctrl_drill.feed_speed = original_feed_speed

        # Publish air controls
        self.ctrl_air.enabled = self.ctrl_air.power > 0
        self.ctrl_air.header.stamp = self.get_rostime()
        self.pub_air.publish(self.ctrl_air)

        # Publish water controls
        self.ctrl_water.header.stamp = self.get_rostime()
        self.pub_water.publish(self.ctrl_water)

        # Publish dust collector controls
        self.ctrl_dust_collector.header.stamp = self.get_rostime()
        self.pub_dust_collector.publish(self.ctrl_dust_collector)

    def set_safe_rotation(self, value: Optional[float] = None) -> None:
        """Set rotation speed with vibration safety check and automatic speed reduction."""
        safe_speed = self.safety_manager.set_safe_rotation(value)
        self.ctrl_drill.rotation_speed = safe_speed




def main():
    rclpy.init()
    node = Driller()
    node.run()


