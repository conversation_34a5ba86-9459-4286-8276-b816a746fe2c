# Driller Node - ROS2 Drilling Control System

Нода управления бурением для робота-бурового станка на ROS2. Реализует конечный автомат состояний (FSM) для полного цикла бурения скважин.

## Архитектура

### Основные компоненты

- **DrillerNode** - главный класс, наследует BaseFSM
- **ProfileManager** - управление профилями бурения и глубинными таблицами  
- **SafetyManager** - система безопасности и детекция заклинивания
- **11 состояний FSM** - полный цикл бурения от детекции грунта до подъема

### Состояния FSM

| Состояние | Назначение | Переходы |
|-----------|------------|----------|
| **idle** | Ожидание команды | → touchdown/drilling |
| **touchdown** | Детекция касания грунта | → overburden_pass |
| **overburden_pass** | Проход верхнего слоя | → drilling |
| **drilling** | Основное бурение | → pullup/wait_after_drill/hard_rot/unstuck/pass_soft |
| **hard_rot** | Освобождение вращения | → prev/unstuck |
| **pullup** | Протяжка бура | → after_pullup |
| **after_pullup** | Возврат после протяжки | → pass_soft/drilling |
| **pass_soft** | Мягкие участки | → drilling |
| **wait_after_drill** | Подготовка к подъему | → raise |
| **raise** | Подъем бура | → idle |
| **unstuck** | Освобождение заклинивания | → prev |

## Входные топики

### Команды управления
- `/driller_action` (`drill_msgs/DrillerAction`) - команда бурения
- `/rock_type` (`drill_msgs/RockType`) - тип породы и наличие воды

### Состояние системы  
- `/drill_state` (`drill_msgs/DrillState`) - состояние бурового оборудования
- `/vibration_state` (`drill_msgs/VibrationArray`) - спектр вибраций для защиты по вибрациям
- `/arm_controller_status` (`drill_msgs/StateMachineStatus`) - статус контроллера люнета
- `/main_mode` (`common_msgs/VehicleBehaviorMode`) - режим поведения машины
- `/robomode` (`drill_msgs/BoolStamped`) - режим робота 
(автоматически)
- `/permission` (`drill_msgs/Permission`) - разрешения на 
движение (автоматически)

## Выходные топики

### Управление бурением
- `/driller_setpoints` (`drill_msgs/DrillCtrl`) - уставки управления бурением
- `/water_ctrl_auto` (`drill_msgs/FloatCtrl`) - управление подачей воды
- `/air_ctrl` (`drill_msgs/AirCtrl`) - управление компрессором
- `/arm_action` (`drill_msgs/OpenCloseAction`) - команды люнету
 - `/dust_collector_ctrl` (`common_msgs/BoolStamped`) - управление пылеудалением

### Системные (автоматически)
- `/events` - события системы
- `/internal_report` - внутренние отчеты  
- `/driller_status` (`drill_msgs/StateMachineStatus`) - статус FSM

## Сервисы

- `/driller/recalibrate_air` (`std_srvs/Trigger`) - калибровка номинального давления воздуха

## Система профилей

### Базовые профили
- **default** - базовые параметры для всех состояний
- **default-short** - для коротких скважин (< 9.5м)

### Динамические профили (по типу породы)
- **hard** - твердые породы (увеличенное давление)
- **wet** - скважины с водой (дополнительные протяжки)
- **cracked** - трещиноватые породы (специальные протяжки)
- **wet-short**, **cracked-short** - комбинированные профили

### Глубинные таблицы

Поддерживаются следующие ссылки на глубину:
- `from_ground` - от поверхности земли
- `from_added_shaft_start` - от начала текущей штанги
- `from_shaft_finish` - от конца текущей штанги  
- `from_hole_finish` - от конца скважины
- `drilled` - от начала текущего состояния

## Система безопасности

### Детекция заклинивания
- **is_stuck** - общее заклинивание движения
- **is_hard_rot** - заклинивание вращения
- Критерии по скорости, давлению, времени

### Контроль воздуха
- Номинальное давление и превышения
- Диагностика компрессора
- Автоматические протяжки при высоком давлении

### Валидация данных
- Проверка достоверности глубины
- Таймауты по состояниям
- Сглаживание скорости бурения

## Конфигурация

### Параметры ноды (`nodes.yaml`)
```yaml
driller:
  rate: 20.0                    # Частота цикла (Гц)
  user_feed_pressure: 80.0      # Базовое давление подачи (бар)
  user_rotation_speed: 115.0    # Максимальная скорость вращения (об/мин)
  timeouts:
    drill_state: 0.5            # Таймаут состояния бурения (с)
    rock_type: 5.0              # Таймаут типа породы (с)
    driller_action: 1.0         # Таймаут команды бурения (с)
```

### Профили бурения (`drilling_profiles.yaml`)
Полная конфигурация профилей в секции `Driller` с параметрами для каждого состояния и специальными профилями.

## Запуск

```bash
# Запуск ноды
ros2 run driller driller

# Запуск через launch файл
ros2 launch driller driller.launch.py
```

## Примеры использования

### Команда бурения
```bash
ros2 topic pub /driller_action drill_msgs/DrillerAction "
header:
  stamp: {sec: 0, nanosec: 0}
id: 30
drill_spindle_depth: 12.348
raise_spindle_depth: 1.27
first_rod_flag: true
last_rod_flag: true  
hole_depth: 11.0
inclination: 0.0"
```

### Калибровка воздуха
```bash
ros2 service call /driller/recalibrate_air std_srvs/Trigger
```

## Мониторинг

### Статус FSM
```bash
ros2 topic echo /driller_status
```

### События системы
```bash  
ros2 topic echo /events
```

### Управляющие сигналы
```bash
ros2 topic echo /driller_setpoints
```

## Детальное описание состояний

### 1. IdleState - Ожидание команды
**Функции:**
- Сброс всех управлений в ноль
- Сброс счетчиков и флагов
- Валидация данных глубины перед стартом
- Выбор следующего состояния по `first_rod_flag`

**Переходы:**
- `first_rod_flag=true` → `touchdown` (детекция грунта)
- `first_rod_flag=false` → `drilling` (прямо к бурению)

### 2. TouchdownState - Детекция касания грунта
**Функции:**
- Медленная подача вниз без вращения
- Детекция грунта по снижению скорости
- Сохранение глубины грунта для будущих расчетов

**Параметры:**
- `feed_speed_ctrl: 0.2` - медленная подача
- `ground_detection_speed_threshold: 0.05` - порог детекции
- `max_touchdown_depth: 2.0` - максимальная глубина поиска

### 3. OverburdenPassState - Проход верхнего слоя
**Функции:**
- Плавный спин-ап вращения
- Калибровка номинального давления воздуха
- Переход к основному бурению

**Параметры:**
- `rotation_spinup_time: 3.0` - время разгона вращения
- `air_calibration_time: 2.0` - время калибровки воздуха
- `overburden_depth: 1.5` - глубина верхнего слоя

### 4. DrillingState - Основное бурение ⭐
**Функции:**
- I-регулятор давления подачи по скорости бурения
- Обязательные протяжки по глубинным таблицам
- Управление водой по глубинным таблицам
- Детекция заклинивания и переходы в аварийные режимы

**Ключевые алгоритмы:**
- **PID регулятор:** Только I-составляющая для стабилизации скорости
- **Обязательные протяжки:** По глубинным ссылкам
- **Ограничения давления:** По высокому давлению вращения и глубинным таблицам
- **Плавное нарастание:** Медленное увеличение давления при входе

### 5. HardRotState - Освобождение вращения
**Функции:**
- Подъем бура для освобождения заклинившего вращения
- Остановка вращения на время подъема
- Возврат в предыдущее состояние при освобождении

**Параметры:**
- `min_lift_time: 1.0` - минимальное время подъема
- `max_lift_time: 5.0` - таймаут подъема
- `max_lift_distance: 0.5` - максимальное расстояние подъема

### 6. PullUpState - Протяжка бура
**Функции:**
- Различные типы протяжек: short, long, cracked, air
- Трехфазная последовательность: подъем → пауза → завершение
- Управление водой в зависимости от типа протяжки

**Типы протяжек:**
- **short:** 0.8м подъем, 1.5с пауза, вода 0.4
- **long:** 2.0м подъем, 3.0с пауза, вода 0.8
- **cracked:** 1.0м подъем, 2.0с пауза, вода 0.6
- **air:** 1.5м подъем, 2.5с пауза, вода 0.7

### 7. AfterPullUpState - Возврат после протяжки
**Функции:**
- Медленный возврат к глубине бурения
- Повторная детекция грунта при спуске
- Постепенное восстановление подачи воды

**Фазы:**
- **До грунта:** Быстрый спуск без вращения
- **После грунта:** Медленный спуск с вращением
- **Восстановление воды:** Плавное увеличение за 3 секунды

### 8. PassSoftState - Мягкие участки
**Функции:**
- Пониженное давление для мягких пород
- Мониторинг давления вращения для выхода из режима
- Переходы обратно в обычное бурение

**Критерии:**
- **Вход:** Низкое давление вращения < 100 бар
- **Выход:** Высокое давление вращения > 150 бар
- **Таймаут:** Максимум 30 секунд в режиме

### 9. WaitAfterDrillState - Подготовка к подъему
**Функции:**
- Остановка подачи, продолжение вращения
- Очистка скважины воздухом и водой
- Подготовка к подъему бура

**Параметры:**
- `cleaning_time: 3.0` - время очистки
- `cleaning_rotation_speed: 60.0` - скорость для очистки
- `cleaning_water_ctrl: 0.5` - вода для очистки

### 10. RaiseState - Подъем бура
**Функции:**
- Подъем с переменной скоростью по глубинным таблицам
- Очистка скважины во время подъема
- Переход в idle при достижении целевой глубины

**Особенности:**
- Скорость подъема зависит от глубины
- Постоянная очистка воздухом и водой
- Контроль достижения `raise_spindle_depth`

### 11. UnstuckState - Освобождение от заклинивания
**Функции:**
- Циклы вверх-вниз для освобождения бура
- Двухфазные циклы: подъем → спуск
- Возврат в предыдущее состояние при освобождении

**Алгоритм:**
- **Фаза вверх:** 2.0с подъем без вращения
- **Фаза вниз:** 1.5с спуск с вращением 30 об/мин
- **Максимум:** 5 циклов или 30 секунд

## Система безопасности (детально)

### Детекция заклинивания
```python
# Критерии заклинивания вращения
rotation_stuck = (command_rotation > 0.95) and (actual_rpm < 3.0)

# Критерии заклинивания движения
movement_stuck = (command_up < -0.95) and (actual_speed > -0.05)

# Высокое давление вращения
high_pressure = rotation_pressure > 310.0
```

### Контроль воздуха
```python
# Номинальное давление
is_nominal = (pressure <= 5.5) and (pressure <= nominal + 0.6)

# Диагностика компрессора
expected = 0.7 if compressor_on else 0.3
compressor_ok = (on and pressure > 0.7) or (off and pressure < 0.3)
```

### Валидация глубины
```python
# Таймауты по состояниям
timeout = 8.0 if state in ['idle', 'drilling', 'after_pullup', 'pass_soft'] else 0.6
valid = (head_pos_is_reliable) or (time_since_valid < timeout)
```
