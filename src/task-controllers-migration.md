# Задание по переносу контроллеров ROS1 на ROS2

## Обзор

Необходимо перенести четыре контроллера бурового оборудования с ROS1 на ROS2 с использованием унифицированной архитектуры на базе BaseFSM. Контроллеры управляют исполнительными механизмами через конечные автоматы состояний.

## Анализ существующих контроллеров

### Общая архитектура ROS1 контроллеров

Все контроллеры следуют единому паттерну:
- **Базовый класс**: `AbstractNodeStateMachine` (ROS1)
- **Состояния**: Наследники `AbstractState` с методами `do_work()`, `on_transition_to()`, `on_transition_from()`
- **Входы**: Подписки на состояния датчиков и команды действий
- **Выходы**: Публикация команд управления исполнительными механизмами
- **Безопасность**: Проверки режимов работы, таймауты, обработка ошибок

### 1. Arm Controller (Контроллер люнета)

**Функция**: Управление открытием/закрытием люнета бурового станка

**Состояния FSM**:
- `OPEN` - люнет открыт (ожидание команд)
- `OPENING` - процесс открытия люнета
- `CLOSED` - люнет закрыт (ожидание команд)  
- `CLOSING` - процесс закрытия люнета

**Входы**:
- `/arm_switch_state` (ArmState) - состояние концевых выключателей люнета
- `/arm_action` (Action) - команды управления (JSON: `{"open": true/false}`)
- `/main_mode` (VehicleBehaviorMode) - режим работы системы
- `/drill_state` (DrillState) - состояние бурового оборудования

**Выходы**:
- `/arm_control` (FloatStamped) - команда управления (-1: открыть, 0: стоп, 1: закрыть)

**Логика безопасности**:
- Запрет закрытия при глубокой позиции бурового инструмента
- Контроль таймаутов операций (opening_time, closing_time)
- Проверка соответствия фактического состояния концевиков

### 2. Tower Controller (Контроллер мачты)

**Функция**: Управление наклоном мачты и блокировочными штифтами

**Состояния FSM**:
- `IDLE` - ожидание команд
- `LOCKING` - блокировка штифтов
- `UNLOCKING` - разблокировка штифтов
- `ARM_CLOSING` - закрытие люнета перед наклоном
- `TILT_REGULATION` - регулирование угла наклона

**Входы**:
- `/tower_switch_state` (TowerSwitchState) - состояние штифтов
- `/tower_state` (TowerAngle) - текущий угол наклона мачты
- `/tower_action` (Action) - команды (JSON: `{"tower_angle": degrees}`)
- `/arm_controller_status` (StateMachineStatus) - статус контроллера люнета

**Выходы**:
- `/tower_control` (TowerCtrlStamped) - команды управления мачтой
- `/arm_action` (Action) - команды люнету при необходимости

### 3. Wrench Controller (Контроллер ключа)

**Функция**: Управление ключом страгивания труб

**Состояния FSM**:
- `OPEN` - ключ отведен
- `OPENING` - процесс отведения ключа
- `CLOSED` - ключ подведен к трубе
- `CLOSING` - процесс подведения ключа
- `TURN` - поворот ключа (страгивание)
- `RELEASE` - освобождение после поворота

**Входы**:
- `/wrench_switch_state` (WrenchSwitchState) - состояние концевиков ключа
- `/wrench_action` (Action) - команды управления

**Выходы**:
- `/wrench_control` (WrenchCtrlStamped) - команды управления ключом

### 4. Carousel Controller (Контроллер карусели)

**Функция**: Управление поворотом карусели с трубами

**Состояния FSM**:
- `IDLE` - ожидание команд
- `CLOSING` - закрытие карусели
- `TURN_CW` - поворот по часовой стрелке
- `TURN_CCW` - поворот против часовой стрелки

**Входы**:
- `/carousel_switch_state` (CarouselSwitchState) - состояние концевиков
- `/carousel_action` (Action) - команды поворота

**Выходы**:
- `/carousel_control` (CarouselCtrlStamped) - команды управления каруселью

## Унифицированная архитектура для ROS2

### Базовая структура контроллера

Все контроллеры должны наследоваться от `BaseFSM` и следовать единому паттерну:

```python
from base_node.base_fsm import BaseFSM, BaseState
from drill_msgs.msg import StateCommand

class ControllerFSM(BaseFSM):
    def __init__(self):
        super().__init__(node_name="controller_name")
        
        # Регистрация состояний
        self.add_states(
            IdleState(self),
            WorkingState(self),
            # ... другие состояния
        )
        
        # Регистрация подписчиков с таймаутами
        self.add_subscriber("/sensor_topic", SensorMsg, "sensor", timeout=1.0)
        
        # Создание публикаторов
        self.control_pub = self.create_publisher(ControlMsg, "/control_topic", 10)
        
        # Установка начального состояния
        self.set_state("idle")
    
    def stop_control(self):
        """Безопасная остановка исполнительных механизмов"""
        # Публикация нулевых команд
        
    def safety_check(self) -> bool:
        """Проверки безопасности"""
        # Проверка режимов работы, таймаутов датчиков и т.д.
        return True
```

### Унифицированные состояния

Базовые состояния для всех контроллеров:

```python
class IdleState(BaseState):
    """Состояние ожидания команд"""
    def __init__(self, node):
        super().__init__(name="idle", node=node, remember_as_prev=True)
    
    def do_work(self):
        self.node.stop_control()
        # Обработка входящих команд

class WorkingState(BaseState):
    """Базовое рабочее состояние"""
    def __init__(self, node, name):
        super().__init__(name=name, node=node, remember_as_prev=True)
    
    def do_work(self):
        # Проверка завершения операции
        # Публикация команд управления
```

### Система команд

Унифицированная система команд через топик `/set_state`:

```python
# Для внешнего управления состояниями
state_cmd = StateCommand()
state_cmd.node_name = "arm_controller"
state_cmd.state = "opening"  # или "prev" для возврата
```

## Требования к реализации

### Общие требования

1. **Базовый класс**: Наследование от `BaseFSM`
2. **Архитектура**: Следование паттернам из `BaseFSM.md`
3. **Параметры**: Использование системы параметров BaseNode (не ROS2 параметры)
4. **Логирование**: Использование методов `log()` и `handle_error()` из BaseNode
5. **Безопасность**: Реализация `stop_control()` и `safety_check()`
6. **Время**: Использование `get_time()` и `get_rostime()` из BaseNode

### Структура пакета

```
src/controllers/
├── arm_controller/
│   ├── arm_controller/
│   │   ├── __init__.py
│   │   ├── arm_controller.py      # Основной FSM класс
│   │   └── states.py              # Классы состояний
│   ├── launch/
│   │   └── arm_controller.launch.xml
│   ├── README.md
│   ├── package.xml
│   └── setup.py
├── tower_controller/
├── wrench_controller/
└── carousel_controller/
```

### Интерфейсы ROS2

**Входные топики** (автоматически через BaseFSM):
- `/permission` (Permission) - разрешения системы безопасности
- `/robomode` (BoolStamped) - режим работы робота
- `/main_state_machine_status` (StateMachineStatus) - статус главного автомата

**Выходные топики** (автоматически через BaseFSM):
- `/events` (Event) - события для системы мониторинга
- `/internal_report` (Report) - отчеты о состоянии ноды
- `/{node_name}_status` (StateMachineStatus) - статус FSM

## Приоритет реализации

### Этап 1: Arm Controller (Приоритет 1)

Начать с контроллера люнета как наиболее простого и критически важного.

**Специфические входы**:
- `/arm_switch_state` (ArmState) - состояние концевиков
- `/drill_state` (DrillState) - состояние бурового оборудования  

**Специфические выходы**:
- `/arm_control` (FloatStamped) - команда управления

**Состояния**:
- `idle` - ожидание (начальное состояние)
- `opening` - открытие люнета
- `open` - люнет открыт
- `closing` - закрытие люнета  
- `closed` - люнет закрыт

**Параметры** (добавить в `nodes.yaml`):
```yaml
ArmController:
  opening_time: 10.0      # Таймаут открытия (сек)
  closing_time: 8.0       # Таймаут закрытия (сек)
  open_push_time: 1.0     # Время удержания в открытом состоянии
  no_reaction_time: 3.0   # Таймаут отсутствия реакции
  max_depth_to_close: 0.5 # Максимальная глубина для закрытия
```

### Этап 2-4: Остальные контроллеры

После успешной реализации arm_controller, перенести остальные контроллеры по аналогичной схеме.

## Особенности переноса

### Изменения в сообщениях

ROS1 → ROS2 изменения:
- `rospy.Time.now()` → `self.get_rostime()`
- `rospy.get_time()` → `self.get_time()`
- Обработка JSON команд остается аналогичной

### Система безопасности

Интеграция с системой безопасности BaseFSM:
- Автоматические проверки `/permission` и `/robomode`
- Таймауты подписчиков
- Переход в состояние `failure` при критических ошибках

### Обработка ошибок

Использование унифицированной системы:
```python
# Предупреждение без перехода в failure
self.log("Warning message", level=self.WARN, event_code=self.events.WARNING_CODE)

# Ошибка с переходом в failure  
self.handle_error("Critical error", level=self.ERROR, event_code=self.events.ERROR_CODE)
```

## Детальные спецификации контроллеров

### Arm Controller - Детальная спецификация

**Алгоритм работы**:

1. **Состояние IDLE**:
   - Остановка управления (`arm_ctrl = 0`)
   - Ожидание команд через `/set_state` или Action
   - Проверка соответствия фактического состояния

2. **Состояние OPENING**:
   - Подача команды открытия (`arm_ctrl = -1`)
   - Контроль таймаута `opening_time`
   - Переход в OPEN при срабатывании концевика `open`
   - Обработка ошибок при превышении таймаута

3. **Состояние OPEN**:
   - Удержание в открытом состоянии `open_push_time`
   - Контроль фактического состояния концевиков
   - Автовосстановление при потере позиции

4. **Состояние CLOSING**:
   - Проверка безопасности (глубина бурения < `max_depth_to_close`)
   - Подача команды закрытия (`arm_ctrl = 1`)
   - Контроль таймаутов и реакции системы
   - Переход в CLOSED при срабатывании концевика

5. **Состояние CLOSED**:
   - Проверка безопасности по глубине бурения
   - Автоматическое открытие при превышении глубины
   - Контроль фактического состояния

**События безопасности**:
- `rc_open_arm` - не удалось открыть люнет
- `rc_close_arm` - не удалось закрыть люнет
- `head_too_low` - бурголовка слишком низко для закрытия
- `already_done` - уже в запрошенном состоянии

### Tower Controller - Детальная спецификация

**Сложная логика взаимодействия**:
- Координация с arm_controller (автоматическое закрытие люнета)
- Последовательность: разблокировка → наклон → блокировка
- PID-регулирование угла наклона

**Алгоритм**:
1. Получение команды с целевым углом
2. Проверка необходимости закрытия люнета
3. Разблокировка штифтов (UNLOCKING)
4. Регулирование угла (TILT_REGULATION)
5. Блокировка в новом положении (LOCKING)

### Wrench Controller - Детальная спецификация

**Многоступенчатая операция поворота**:
1. CLOSING - подведение к трубе
2. TURN - поворот ключа (страгивание)
3. RELEASE - освобождение после поворота
4. OPENING - отведение ключа

**Особенности**:
- Контроль нескольких ступеней зажима
- Проверка консистентности концевиков
- Таймауты для каждой операции

### Carousel Controller - Детальная спецификация

**Двухэтапная операция**:
1. Поворот к нужному индексу (TURN_CW/TURN_CCW)
2. Закрытие карусели (CLOSING)

**Логика поворота**:
- Определение направления по текущему и целевому индексу
- Контроль достижения позиции по концевикам
- Автоматическое закрытие после позиционирования

## Унификация и упрощение

### Общие паттерны для извлечения

1. **Базовое состояние ожидания**:
```python
class IdleState(BaseState):
    def __init__(self, node, next_states_map):
        super().__init__(name="idle", node=node)
        self.next_states = next_states_map

    def do_work(self):
        self.node.stop_control()
        # Универсальная обработка команд
```

2. **Базовое рабочее состояние с таймаутом**:
```python
class TimedOperationState(BaseState):
    def __init__(self, node, name, timeout_param, control_value):
        super().__init__(name=name, node=node)
        self.timeout = timeout_param
        self.control_value = control_value

    def do_work(self):
        # Универсальная логика с таймаутом
```

3. **Проверка концевиков**:
```python
def check_switch_consistency(self, switch_state, open_field, closed_field):
    """Универсальная проверка консистентности концевиков"""
    if getattr(switch_state, open_field) and getattr(switch_state, closed_field):
        return False  # Ошибка - оба концевика активны
    return True
```

### Система параметров

Добавить в `params_server/params_server/base_config/nodes.yaml`:

```yaml
# Контроллеры
ArmController:
  rate: 10.0
  opening_time: 10.0
  closing_time: 8.0
  open_push_time: 1.0
  no_reaction_time: 3.0
  max_depth_to_close: 0.5
  allowed_modes: ["shaft_buildup", "shaft_stow"]

TowerController:
  rate: 10.0
  locking_time: 15.0
  unlocking_time: 15.0
  tilt_regulation_timeout: 30.0
  min_angle_delta: 1.0
  tilt_speed_max: 0.5

WrenchController:
  rate: 10.0
  opening_time: 8.0
  closing_time: 8.0
  turn_time: 10.0
  release_time: 5.0

CarouselController:
  rate: 10.0
  max_turn_time: 20.0
  max_oc_time: 10.0
  rotate_speed: 0.3
```

## Результат

В результате получим четыре унифицированных контроллера на ROS2 с:
- Единой архитектурой на базе BaseFSM
- Автоматической интеграцией с системой безопасности
- Централизованным логированием и мониторингом
- Возможностью внешнего управления состояниями
- Полной совместимостью с существующей системой параметров
- Упрощенной и унифицированной кодовой базой
- Улучшенной диагностикой и обработкой ошибок
