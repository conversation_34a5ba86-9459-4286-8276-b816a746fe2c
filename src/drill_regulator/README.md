# Нода drill_regulator (ROS2)

Назначение: низкоуровневый регулятор бурения с тремя PID-контроллерами:
- скорость подачи (feed)
- скорость вращения (rotation)
- давление подачи (pressure)

Входные топики:
- `/driller_setpoints`, `/rod_changer_setpoints`, `/main_sm_drill_out`, `/remote_drilling` — уставки `drill_msgs/DrillCtrl`
- `drill_state` — состояние `drill_msgs/DrillState`
- `main_state_machine_status` — режим `drill_msgs/StateMachineStatus`
- `robomode` — `drill_msgs/BoolStamped`
- `permission` — `drill_msgs/Permission`

Выходной топик:
- `drill_ctrl` — `drill_msgs/DrillActuatorCtrl` (всегда нормализовано [-1..1])

Параметры (nodes.yaml/drill_regulator_node): `rate`, `msg_timeout`, `pdf_free_moving`, `default_rot_torque`, блоки `feed_reg`, `press_reg`, `rot_reg`.

Семантика уставок:
- `*_is_raw = true` — уставка в абсолютных единицах (м/с, об/мин, бар), проходит через PID.
- `*_is_raw = false` — уставка нормализована [-1..1], публикуется напрямую (с насыщением), без PID.

Безопасность:
- контроль свежести уставок и обратной связи
- проверка разрешений и режима
- аварийный сброс выходов и PID при нарушениях


