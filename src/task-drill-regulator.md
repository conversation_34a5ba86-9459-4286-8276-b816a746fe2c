# Задача: Перевод drill_regulator с ROS1 на ROS2

## Обзор

Необходимо перевести модуль `drill_regulator` с ROS1 на ROS2 с использованием новой архитектуры BaseNode и современных интерфейсов.

**Назначение модуля:** Низкоуровневый регулятор бурения, реализующий три PID контроллера для управления:
- Скоростью подачи (feed_speed)
- Скоростью вращения (rotation_speed) 
- Давлением подачи (feed_pressure)

**Принцип работы:** Получает уставки от высокоуровневых контроллеров (driller, rod_changer, main_sm) и преобразует их в нормализованные команды [-1..1] для исполнительных механизмов.

## Входы и выходы

### На входе

1) **Уставки от driller ноды**
`/driller_setpoints`
```
drill_msgs/DrillCtrl
std_msgs/Header header
float32 feed_speed          # м/с или [-1..1] если is_raw=false
float32 rotation_speed      # об/мин или [-1..1] если is_raw=false
float32 feed_pressure       # бар или [-1..1] если is_raw=false
bool feed_speed_is_raw      # true = абсолютные единицы, false = нормализованные
bool rotation_speed_is_raw  # true = абсолютные единицы, false = нормализованные
bool feed_pressure_is_raw   # true = абсолютные единицы, false = нормализованные
```

2) **Уставки от rod_changer ноды**
`/rod_changer_setpoints`
```
drill_msgs/DrillCtrl  # Та же структура что и driller_setpoints
```

3) **Состояние бурения** (обратная связь)
`/drill_state`
```
drill_msgs/DrillState
std_msgs/Header header
float32 head_pos            # Позиция головы шпинделя (м)
float32 head_speed          # Скорость движения головы (м/с) - ОБРАТНАЯ СВЯЗЬ для feed PID
float32 head_angular_pos    # Угловая позиция (градусы)
float32 drill_rpm           # Скорость вращения бура (об/мин) - ОБРАТНАЯ СВЯЗЬ для rotation PID
float32 feed_pressure       # Давление подачи (бар) - ОБРАТНАЯ СВЯЗЬ для pressure PID
float32 rot_pressure        # Давление вращения (бар)
float32 air_pressure        # Давление воздуха (бар)
bool head_pos_is_reliable   # Флаг достоверности позиции головы
```

4) **Статус главной машины состояний**
`/main_state_machine_status`
```
drill_msgs/StateMachineStatus
std_msgs/Header header
int32 cur_action_id
int32 last_action_id
string current_state        # Используется для определения активного режима
```

5) **Режим робота** (автоматически подписывается через BaseFSM)
`/robomode`
```
drill_msgs/BoolStamped
std_msgs/Header header
bool value
```

6) **Разрешения на движение** (автоматически подписывается через BaseFSM)
`/permission`
```
drill_msgs/Permission
std_msgs/Header header
bool permission
string source
```

### На выходе

1) **Управляющие команды для исполнительных механизмов**
`/drill_ctrl`
```
drill_msgs/DrillActuatorCtrl
std_msgs/Header header
float32 feed_speed          # [-1..1] нормализованная скорость подачи
float32 rotation_speed      # [-1..1] нормализованная скорость вращения
float32 feed_pressure       # [-1..1] нормализованное давление подачи
float32 torque_limit        # [-1..1] ограничение крутящего момента
```

2) **События системы** (автоматически через BaseNode)
`/events`

3) **Внутренние отчеты** (автоматически через BaseNode)
`/internal_report`

## Анализ старой ROS1 реализации

### Архитектура старой ноды

**Основные компоненты:**
- **DrillRegulatorNode** - главный класс, наследует BaseNode (ROS1)
- **Три PID контроллера** - feed_pid, press_pid, rot_pid
- **Система приоритетов** - разные источники уставок в зависимости от режима
- **Система безопасности** - проверка таймаутов, разрешений, режимов

### Логика работы

**Приоритеты источников уставок по режимам:**
- `DRILLING` → уставки от driller ноды
- `CALIB`, `POST_CALIB`, `RESTORE_STRING` → уставки от main_state_machine (`/main_sm_drill_out`)
- `SHAFT_BUILDUP`, `SHAFT_STOW` → уставки от rod_changer
- `REMOTE` → уставки от remote_drilling
- Остальные режимы → остановка (обнуление управления)

**Система безопасности:**
- Проверка актуальности сообщений (node param `msg_timeout`)
- Проверка разрешений на движение (`/robomode`, `/permission`)
- Проверка допустимых режимов работы
- Автоматическая остановка при ошибках

**PID контроллеры:**
- **feed_pid**: уставка (м/с) → обратная связь (head_speed) → выход [-1..1]
- **press_pid**: уставка (бар) → обратная связь (feed_pressure) → выход [-1..1]
- **rot_pid**: уставка (об/мин) → обратная связь (drill_rpm) → выход [-1..1]

**Выход:** Всегда нормализованные значения [-1..1] в `drill_msgs/DrillCtrl` с флагами `*_is_raw = false`

## Требования к ROS2 реализации

### Архитектура

- **Наследование**: `DrillRegulatorNode` ← `BaseNode` (`src/base_node/base_node/base_node.py`)
- **PID контроллеры**: использовать готовый `PID` из `src/base_node/base_node/pid.py`
- **Логирование**: через `BaseNode.log()` и коды из `BaseNode.events`
- **Параметры**: только через `params_server`, НЕ через ROS2 параметры
- **Частота цикла**: параметр `rate` (Гц) из `nodes.yaml`
- **Подписки**: через `create_subscription()` с обязательными таймаутами

### Ключевые улучшения архитектуры

1. **Использование готового PID контроллера**
```python
from base_node.base_node.pid import PID

# Вместо старого PID создавать:
self.feed_pid = PID(
    get_time=self.get_time,
    node=self,
    p=self.node_params['feed_reg']['p'],
    i=self.node_params['feed_reg']['i'],
    d=self.node_params['feed_reg']['d'],
    ff=self.node_params['feed_reg']['ff'],
    i_saturation=self.node_params['feed_reg']['i_saturation'],
    out_min=self.node_params['feed_reg']['out_min'],
    out_max=self.node_params['feed_reg']['out_max'],
    d_term_tc=self.node_params['feed_reg']['d_term_tc'],
    out_tc=self.node_params['feed_reg']['out_tc'],
    force_zero=True,
    name="FeedPID",
    enable_logging=True
)
```

2. **Современная обработка ошибок**
```python
# Вместо handle_internal_error() использовать:
self.log(
    "Устаревшие данные состояния бурения",
    level=self.ERROR,
    event_code=self.global_params.events.SENSOR_TIMEOUT
)
```

3. **Типизированные структуры данных**
```python
from dataclasses import dataclass
from typing import Optional

@dataclass
class RegulatorState:
    """Состояние регулятора"""
    feed_setpoint: float = 0.0
    rotation_setpoint: float = 0.0
    pressure_setpoint: float = 0.0
    
    feed_feedback: float = 0.0
    rotation_feedback: float = 0.0
    pressure_feedback: float = 0.0
    
    feed_output: float = 0.0
    rotation_output: float = 0.0
    pressure_output: float = 0.0
    
    last_setpoint_time: Optional[float] = None
    last_feedback_time: Optional[float] = None
```

## Параметры конфигурации

### Параметры ноды (nodes.yaml/drill_regulator_node)
```yaml
drill_regulator_node:
  rate: 50.0  # Частота работы регулятора (Гц)
  
  # Параметры PID регулятора подачи
  feed_reg:
    p: 1.0
    i: 0.5
    d: 0.1
    ff: 0.0
    i_saturation: 1.0
    out_min: -1.0
    out_max: 1.0
    d_term_tc: 0.01
    out_tc: 0.01
  
  # Параметры PID регулятора давления
  press_reg:
    p: 0.8
    i: 0.3
    d: 0.05
    ff: 0.0
    i_saturation: 1.0
    out_min: -1.0
    out_max: 1.0
    d_term_tc: 0.01
    out_tc: 0.01
  
  # Параметры PID регулятора вращения
  rot_reg:
    p: 1.2
    i: 0.4
    d: 0.08
    ff: 0.0
    i_saturation: 1.0
    out_min: -1.0
    out_max: 1.0
    d_term_tc: 0.01
    out_tc: 0.01
  
  # Безопасность
  msg_timeout: 0.5  # Таймаут сообщений (сек)
  pdf_free_moving: 0.1  # Давление при свободном движении вниз
  default_rot_torque: 0.5  # Крутящий момент по умолчанию
```

## Детальная спецификация реализации

### Главный класс DrillRegulatorNode

```python
from typing import Optional, Dict, Any
from dataclasses import dataclass
from base_node.base_node.base_node import BaseNode
from base_node.base_node.pid import PID
from drill_msgs.msg import DrillCtrl, DrillActuatorCtrl, DrillState, StateMachineStatus

@dataclass
class RegulatorState:
    """Типизированное состояние регулятора"""
    # Уставки
    feed_setpoint: float = 0.0
    rotation_setpoint: float = 0.0
    pressure_setpoint: float = 0.0

    # Обратная связь
    feed_feedback: float = 0.0
    rotation_feedback: float = 0.0
    pressure_feedback: float = 0.0

    # Выходы PID (всегда нормализованные [-1..1])
    feed_output: float = 0.0
    rotation_output: float = 0.0
    pressure_output: float = 0.0

    # Флаги режимов
    pressure_is_raw: bool = False
    feed_is_raw: bool = False
    rotation_is_raw: bool = False

    # Временные метки
    last_setpoint_time: Optional[float] = None
    last_feedback_time: Optional[float] = None

class DrillRegulatorNode(BaseNode):
    """ROS2 нода низкоуровневого регулятора бурения"""

    def __init__(self):
        super().__init__('drill_regulator_node')

        # Состояние регулятора
        self.state = RegulatorState()
        self.current_mode: Optional[str] = None

        # PID контроллеры (создаются в initialize)
        self.feed_pid: Optional[PID] = None
        self.press_pid: Optional[PID] = None
        self.rot_pid: Optional[PID] = None

        # Источники уставок
        self.setpoint_sources = {
            'driller': None,
            'rod_changer': None,
            'remote': None
        }

    def initialize(self):
        """Инициализация ноды"""
        # Создание PID контроллеров
        self._create_pid_controllers()

        # Создание подписчиков
        self._create_subscribers()

        # Создание публикаторов
        self._create_publishers()

    def _create_pid_controllers(self):
        """Создание PID контроллеров с параметрами из конфигурации"""
        feed_params = self.node_params['feed_reg']
        self.feed_pid = PID(
            get_time=self.get_time,
            node=self,
            p=feed_params['p'],
            i=feed_params['i'],
            d=feed_params['d'],
            ff=feed_params['ff'],
            i_saturation=feed_params['i_saturation'],
            out_min=feed_params['out_min'],
            out_max=feed_params['out_max'],
            d_term_tc=feed_params['d_term_tc'],
            out_tc=feed_params['out_tc'],
            force_zero=True,
            name="FeedPID",
            enable_logging=True
        )

        press_params = self.node_params['press_reg']
        self.press_pid = PID(
            get_time=self.get_time,
            node=self,
            p=press_params['p'],
            i=press_params['i'],
            d=press_params['d'],
            ff=press_params['ff'],
            i_saturation=press_params['i_saturation'],
            out_min=press_params['out_min'],
            out_max=press_params['out_max'],
            d_term_tc=press_params['d_term_tc'],
            out_tc=press_params['out_tc'],
            force_zero=True,
            name="PressPID",
            enable_logging=True
        )

        rot_params = self.node_params['rot_reg']
        self.rot_pid = PID(
            get_time=self.get_time,
            node=self,
            p=rot_params['p'],
            i=rot_params['i'],
            d=rot_params['d'],
            ff=rot_params['ff'],
            i_saturation=rot_params['i_saturation'],
            out_min=rot_params['out_min'],
            out_max=rot_params['out_max'],
            d_term_tc=rot_params['d_term_tc'],
            out_tc=rot_params['out_tc'],
            force_zero=True,
            name="RotPID",
            enable_logging=True
        )

    def _create_subscribers(self):
        """Создание подписчиков"""
        # Уставки от различных источников
        self.create_subscription(
            DrillCtrl, '/driller_setpoints',
            lambda msg: self._setpoint_callback(msg, 'driller'), 10
        )

        self.create_subscription(
            DrillCtrl, '/rod_changer_setpoints',
            lambda msg: self._setpoint_callback(msg, 'rod_changer'), 10
        )

        self.create_subscription(
            DrillCtrl, '/main_sm_drill_out',
            lambda msg: self._setpoint_callback(msg, 'main_sm'), 10
        )

        self.create_subscription(
            DrillCtrl, '/remote_drilling',
            lambda msg: self._setpoint_callback(msg, 'remote'), 10
        )



        # Обратная связь
        self.create_subscription(
            DrillState, '/drill_state', self._drill_state_callback, 10
        )

        # Статус главной машины состояний
        self.create_subscription(
            StateMachineStatus, '/main_state_machine_status',
            self._main_sm_status_callback, 10
        )

    def _create_publishers(self):
        """Создание публикаторов"""
        self.drill_ctrl_pub = self.create_publisher(
            DrillActuatorCtrl, 'drill_ctrl', 10
        )

    def do_work(self):
        """Основной рабочий цикл регулятора"""
        # Проверка готовности к работе
        if not self._safety_check():
            self._stop_control()
            self._publish_control()
            return

        # Выбор активного источника уставок
        active_setpoints = self._select_active_setpoints()
        if not active_setpoints:
            self._stop_control()
            self._publish_control()
            return

        # Обновление уставок
        self._update_setpoints(active_setpoints)

        # Работа PID контроллеров
        self._update_pid_controllers()

        # Публикация управления
        self._publish_control()

    def _safety_check(self) -> bool:
        """Проверка безопасности работы"""
        current_time = self.get_time()
        timeout = self.node_params.get('msg_timeout', 0.5)

        # Проверка актуальности обратной связи
        if (self.state.last_feedback_time is None or
            current_time - self.state.last_feedback_time > timeout):
            self.log(
                "Устаревшие данные состояния бурения",
                level=self.ERROR,
                event_code=self.events.SENSOR_FAILURE
            )
            return False

        # Проверка актуальности уставок
        if (self.state.last_setpoint_time is None or
            current_time - self.state.last_setpoint_time > timeout):
            self.log(
                "Устаревшие уставки управления",
                level=self.ERROR,
                event_code=self.events.SW_ERROR
            )
            return False

        # Проверка режима работы
        allowed_modes = [
            'drilling', 'calib', 'post_calib', 'restore_string', 'shaft_buildup', 'shaft_stow', 'remote'
        ]
        if self.current_mode not in allowed_modes:
            return False

        return True

    def _select_active_setpoints(self) -> Optional[Dict[str, Any]]:
        """Выбор активного источника уставок по приоритету режима"""
        if self.current_mode == 'drilling':
            return self.setpoint_sources.get('driller')
        elif self.current_mode in ['calib', 'post_calib', 'restore_string']:
            return self.setpoint_sources.get('main_sm')
        elif self.current_mode in ['shaft_buildup', 'shaft_stow']:
            return self.setpoint_sources.get('rod_changer')
        elif self.current_mode == 'remote':
            return self.setpoint_sources.get('remote')

        return None

    def _update_setpoints(self, setpoints: Dict[str, Any]):
        """Обновление уставок из активного источника"""
        self.state.feed_setpoint = setpoints.get('feed_speed', 0.0)
        self.state.rotation_setpoint = setpoints.get('rotation_speed', 0.0)
        self.state.pressure_setpoint = setpoints.get('feed_pressure', 0.0)


        self.state.feed_is_raw = setpoints.get('feed_speed_is_raw', False)
        self.state.rotation_is_raw = setpoints.get('rotation_speed_is_raw', False)
        self.state.pressure_is_raw = setpoints.get('feed_pressure_is_raw', False)

    def _update_pid_controllers(self):
        """Обновление PID контроллеров"""
        # PID регулятор подачи
        if not self.state.feed_is_raw:
            self.state.feed_output = self.feed_pid.update(
                self.state.feed_setpoint,
                self.state.feed_feedback
            )
        else:
            self.feed_pid.reset()
            self.state.feed_output = self.state.feed_setpoint

        # PID регулятор давления
        if not self.state.pressure_is_raw:
            # Специальная логика для свободного движения вниз
            if self.state.pressure_setpoint < 0.001:
                self.press_pid.reset()
                self.state.pressure_output = self.node_params.get('pdf_free_moving', 0.1)
            else:
                self.state.pressure_output = self.press_pid.update(
                    self.state.pressure_setpoint,
                    self.state.pressure_feedback
                )
        else:
            self.press_pid.reset()
            self.state.pressure_output = self.state.pressure_setpoint

        # PID регулятор вращения
        if not self.state.rotation_is_raw:
            # Коррекция обратной связи для отрицательного вращения
            feedback = (self.state.rotation_feedback
                       if self.state.rotation_setpoint >= 0
                       else -self.state.rotation_feedback)

            self.state.rotation_output = self.rot_pid.update(
                self.state.rotation_setpoint,
                feedback
            )
        else:
            self.rot_pid.reset()
            self.state.rotation_output = self.state.rotation_setpoint

    def _stop_control(self):
        """Остановка управления и сброс PID"""
        self.state.feed_output = 0.0
        self.state.rotation_output = 0.0
        self.state.pressure_output = 0.0

        if self.feed_pid:
            self.feed_pid.reset()
        if self.press_pid:
            self.press_pid.reset()
        if self.rot_pid:
            self.rot_pid.reset()

    def _publish_control(self):
        """Публикация управляющих команд"""
        msg = DrillActuatorCtrl()
        msg.header.stamp = self.get_rostime()

        msg.feed_speed = self.state.feed_output
        msg.rotation_speed = self.state.rotation_output
        msg.feed_pressure = self.state.pressure_output

        msg.torque_limit = self.state.torque_limit if self.state.torque_limit > 0.001 else self.node_params.get('default_rot_torque', 0.5)

        self.drill_ctrl_pub.publish(msg)

    # Callback функции
    def _setpoint_callback(self, msg: DrillCtrl, source: str):
        """Обработка уставок от различных источников"""
        self.setpoint_sources[source] = {
            'feed_speed': msg.feed_speed,
            'rotation_speed': msg.rotation_speed,
            'feed_pressure': msg.feed_pressure,

            'feed_speed_is_raw': msg.feed_speed_is_raw,
            'rotation_speed_is_raw': msg.rotation_speed_is_raw,
            'feed_pressure_is_raw': msg.feed_pressure_is_raw,
            'timestamp': self.get_time()
        }

        # Обновляем время последних уставок если это активный источник
        if self._is_active_source(source):
            self.state.last_setpoint_time = self.get_time()

    def _drill_state_callback(self, msg: DrillState):
        """Обработка состояния бурения (обратная связь)"""
        self.state.feed_feedback = msg.head_speed
        self.state.rotation_feedback = msg.drill_rpm
        self.state.pressure_feedback = msg.feed_pressure
        self.state.last_feedback_time = self.get_time()

    def _main_sm_status_callback(self, msg: StateMachineStatus):
        """Обработка статуса главной машины состояний"""
        self.current_mode = msg.current_state.lower()

    def _is_active_source(self, source: str) -> bool:
        """Проверка является ли источник активным для текущего режима"""
        if self.current_mode == 'drilling' and source == 'driller':
            return True
        elif self.current_mode in ['shaft_buildup', 'shaft_stow'] and source == 'rod_changer':
            return True
        elif self.current_mode == 'remote' and source == 'remote':
            return True
        return False

    def on_params_update(self, _keys):
        """Обновление параметров PID при изменении конфигурации"""
        if self.feed_pid:
            feed_params = self.node_params['feed_reg']
            self.feed_pid.p = feed_params['p']
            self.feed_pid.i = feed_params['i']
            self.feed_pid.d = feed_params['d']
            self.feed_pid.ff = feed_params['ff']
            self.feed_pid.i_saturation = feed_params['i_saturation']
            self.feed_pid.out_min = feed_params['out_min']
            self.feed_pid.out_max = feed_params['out_max']
            self.feed_pid.d_term_tc = feed_params['d_term_tc']
            self.feed_pid.out_tc = feed_params['out_tc']

        # Аналогично для press_pid и rot_pid...
        self.log("PID параметры обновлены", level=self.INFO)
```

## Ключевые отличия от ROS1 версии

### Улучшения архитектуры

| Аспект | ROS1 версия | ROS2 версия | Улучшения |
|--------|-------------|-------------|-----------|
| **PID контроллеры** | Самописный PID | Готовый PID из base_node | Профессиональная реализация, логирование |
| **Обработка ошибок** | handle_internal_error() | BaseNode.log() + события | Структурированные события |
| **Типизация** | Слабая типизация | @dataclass, typing | Статическая проверка типов |
| **Параметры** | rosparam | params_server | Централизованное управление |
| **Логирование** | rospy.log* | BaseNode.log() | Единообразное логирование |

### Сохраненная функциональность

- ✅ **Три PID контроллера** (feed, pressure, rotation)
- ✅ **Система приоритетов** источников уставок по режимам
- ✅ **Система безопасности** (таймауты, разрешения, режимы)
- ✅ **Специальная логика** (свободное движение вниз, коррекция вращения)
- ✅ **Нормализованный выход** [-1..1] для исполнительных механизмов

## Требования к реализации

### Обязательные компоненты

1. **Главный класс**: `DrillRegulatorNode(BaseNode)`
2. **PID контроллеры**: Использовать готовый `PID` из `base_node.base_node.pid`
3. **Типизированные данные**: `@dataclass` для состояния регулятора
4. **Система безопасности**: Проверка таймаутов, режимов, разрешений
5. **Обработка ошибок**: Через `BaseNode.log()` с кодами событий

### Параметры конфигурации

**ОБЯЗАТЕЛЬНО добавить в nodes.yaml:**
```yaml
drill_regulator_node:
  rate: 50.0
  msg_timeout: 0.5
  pdf_free_moving: 0.1
  default_rot_torque: 0.5

  feed_reg:
    p: 1.0
    i: 0.5
    d: 0.1
    # ... остальные параметры PID

  press_reg:
    # ... параметры PID давления

  rot_reg:
    # ... параметры PID вращения
```

### Тестирование

- **Юнит-тесты**: PID контроллеры, логика выбора источников
- **Интеграционные тесты**: Работа с различными режимами
- **Тесты безопасности**: Обработка таймаутов, ошибочных данных
- **Тесты производительности**: Частота работы 50 Гц

### Документация

- **README.md**: Полное описание входов, выходов, логики работы
- **Launch файл**: Для запуска ноды с параметрами
- **Диаграммы**: Схема PID контроллеров и потоков данных

**Результат**: ROS2 версия будет иметь ВСЮ функциональность ROS1 версии с современной архитектурой, лучшей типизацией и профессиональными PID контроллерами.

### Ключевые особенности выхода

- **Топик**: `/drill_ctrl`
- **Тип сообщения**: `drill_msgs/DrillActuatorCtrl`
- **Значения**: Всегда нормализованные [-1..1]
- **Флаги**: отсутствуют (всегда нормализованный выход)
- **Назначение**: Прямое управление исполнительными механизмами
