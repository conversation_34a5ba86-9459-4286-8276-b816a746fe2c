# Задание: Перенос Arm Controller на ROS2

## Обзор

Перенести контроллер люнета (arm_controller) с ROS1 на ROS2 с использованием архитектуры BaseFSM. Люнет - это зажимное устройство, которое фиксирует буровые трубы во время операций.

## Функциональные требования

### Основная функция
Управление открытием и закрытием люнета бурового станка с контролем безопасности и автоматическим восстановлением позиции.

### Входы

Подписываться с помощью create_subscription из BaseFSM (base_fsm.py)

1. **Команда управления люнетом**:
   - Топик: /arm_ctrl
   - Тип: drill_msgs/FloatCtrl (src/drill_msgs/msg/FloatCtrl.msg)

2. **Состояние концевиков люнета**:
   - Топик: `/arm_state`
   - Тип: `drill_msgs/ArmState
            Header header
            bool open
            bool closed
            bool grip` 
   - Поля: `open` (bool), `closed` (bool), `grip` (bool)

3. **Состояние бурового оборудования**:
   - Топик: `/drill_state`
   - Тип: `drill_msgs/msg/DrillState`
   - Используется: `head_pos` (float), `head_pos_is_reliable` (bool)

4. **Автоматические входы BaseFSM**:
   - '/set_state' (StateCommand)
   - `/permission` (Permission)
   - `/robomode` (BoolStamped)
   - `/main_state_machine_status` (StateMachineStatus) - подписаться, если нет в автоматических 

### Выходы

1. **Команды управления люнетом**:
   - Топик: `/arm_ctrl`
   - Тип: `drill_msgs/FloatCtrl`
   - Значения: -1.0 (открыть), 0.0 (стоп), 1.0 (закрыть)

2. **Автоматические выходы BaseFSM**:
   - `/events` (Event)
   - `/internal_report` (Report)
   - `/arm_controller_status` (StateMachineStatus)

### Управление

**Внешнее управление через StateCommand**:
```python
# Команда открытия
state_cmd = StateCommand()
state_cmd.node_name = "arm_controller"
state_cmd.state = "opening"

# Команда закрытия  
state_cmd.state = "closing"

# Возврат к предыдущему состоянию
state_cmd.state = "prev"
```

## Архитектура FSM

### Состояния

1. **idle** (начальное):
   - Остановка управления
   - Ожидание команд
   - Проверка соответствия фактического состояния

2. **opening**:
   - Подача команды открытия (-1.0)
   - Контроль таймаута
   - Переход в open при срабатывании концевика

3. **open**:
   - Удержание в открытом состоянии
   - Контроль фактического состояния
   - Автовосстановление при потере позиции

4. **closing**:
   - Проверка безопасности (глубина бурения)
   - Подача команды закрытия (1.0)
   - Контроль таймаутов и реакции

5. **closed**:
   - Проверка безопасности по глубине
   - Автоматическое открытие при превышении
   - Контроль фактического состояния

### Диаграмма переходов

```
    idle
   ↙    ↘
opening  closing
   ↓      ↓
  open   closed
   ↑      ↑
   └──────┘
```

## Логика безопасности

### Проверки безопасности

1. **Глубина бурения**: Запрет закрытия при `drill_state.spindle_depth > max_depth_to_close`
2. **Валидность данных**: Проверка `drill_state.is_valid`
3. **Консистентность концевиков**: Недопустимо одновременное срабатывание `open` и `closed`
4. **Таймауты операций**: Контроль времени выполнения команд

### События безопасности

- `rc_open_arm` - не удалось открыть люнет
- `rc_close_arm` - не удалось закрыть люнет
- `head_too_low` - бурголовка слишком низко для закрытия
- `switch_failure` - неисправность концевых выключателей

## Параметры конфигурации

Добавить в `params_server/params_server/base_config/nodes.yaml`:

```yaml
ArmController:
  rate: 10.0                    # Частота работы FSM (Гц)
  opening_time: 10.0            # Таймаут открытия (сек)
  closing_time: 8.0             # Таймаут закрытия (сек)
  open_push_time: 1.0           # Время удержания в открытом состоянии (сек)
  no_reaction_time: 3.0         # Таймаут отсутствия реакции (сек)
  max_depth_to_close: 0.5       # Максимальная глубина для закрытия (м)
  arm_present: true             # Флаг наличия люнета в системе
  arm_grip_sensor_present: true # Флаг наличия датчика зажима
  allowed_modes:                # Разрешенные режимы работы
    - "shaft_buildup"
    - "shaft_stow"
```

## Структура реализации

### Структура пакета

```
src/controllers/arm_controller/
├── arm_controller/
│   ├── __init__.py
│   ├── arm_controller.py      # Основной FSM класс
│   └── states.py              # Классы состояний
├── launch/
│   └── arm_controller.launch.xml
├── README.md
├── package.xml
└── setup.py
```

### Основной класс FSM

```python
from base_node.base_fsm import BaseFSM
from drill_msgs.msg import ArmState, DrillState, FloatStamped
from .states import IdleState, OpeningState, OpenState, ClosingState, ClosedState

class ArmControllerFSM(BaseFSM):
    def __init__(self):
        super().__init__(node_name="arm_controller")
        
        # Регистрация состояний
        self.add_states(
            IdleState(self),
            OpeningState(self),
            OpenState(self),
            ClosingState(self),
            ClosedState(self)
        )
        
        # Подписчики с таймаутами
        self.add_subscriber("/arm_switch_state", ArmState, "arm_state", timeout=1.0)
        self.add_subscriber("/drill_state", DrillState, "drill_state", timeout=1.0)
        
        # Публикатор команд
        self.control_pub = self.create_publisher(FloatStamped, "/arm_control", 10)
        
        # Установка начального состояния
        self.set_state("idle")
    
    def stop_control(self):
        """Безопасная остановка люнета"""
        msg = FloatStamped()
        msg.header.stamp = self.get_rostime()
        msg.value = 0.0
        self.control_pub.publish(msg)
    
    def safety_check(self) -> bool:
        """Проверки безопасности"""
        # Проверка валидности данных бурения
        if self.subs.drill_state is None or not self.subs.drill_state.is_valid:
            return False
            
        # Проверка консистентности концевиков
        if self.subs.arm_state is not None:
            if self.subs.arm_state.open and self.subs.arm_state.closed:
                self.handle_error(
                    "Inconsistent switch state!",
                    level=self.ERROR,
                    event_code=self.events.SWITCH_FAILURE
                )
                return False
                
        return True
    
    def publish_control(self, value: float):
        """Публикация команды управления"""
        msg = FloatStamped()
        msg.header.stamp = self.get_rostime()
        msg.value = value
        self.control_pub.publish(msg)
```

### Классы состояний

Базовая структура состояний с использованием параметров и проверок безопасности.

## Launch файл

```xml
<?xml version="1.0"?>
<launch>
    <!-- Переменные окружения для BaseNode -->
    <set_env name="PARAM_SERVER_ADDRESS" value="localhost" />
    <set_env name="PARAM_SERVER_PORT" value="5000" />
    <set_env name="REPORT_RATE" value="5.0" />
    
    <!-- Запуск контроллера люнета -->
    <node pkg="arm_controller"
          exec="arm_controller"
          name="arm_controller"
          output="screen"
          respawn="true"
          respawn_delay="5" />
</launch>
```

## Критерии приемки

1. **Функциональность**: Корректное открытие/закрытие люнета по командам
2. **Безопасность**: Запрет закрытия при глубоком бурении
3. **Надежность**: Автовосстановление при потере позиции
4. **Диагностика**: Корректная публикация событий и отчетов
5. **Интеграция**: Работа с системой параметров и BaseFSM
6. **Тестирование**: Покрытие тестами основных сценариев

## Следующие шаги

После успешной реализации arm_controller использовать его как шаблон для переноса остальных контроллеров (tower, wrench, carousel) с аналогичной архитектурой.
