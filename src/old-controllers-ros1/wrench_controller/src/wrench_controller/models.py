#!/usr/bin/env python
# -*- coding: utf-8 -*-

from .constants import OPEN, OPENING, CLOSING, CLOSED, TURN, RELEASE, FAILUR<PERSON>
from state_machine_node import AbstractState
import json
import rospy

class OpenState(AbstractState):
    def __init__(self, node):
        super(OpenState, self).__init__(node, OPEN)

    def on_transition_to(self):
        self.node._cur_action_seq = -1

    def do_work(self):
        self.node.stop_control()
        if not self.node.subs.wrench_state.stage_1_open:
            self.node.set_state(OPENING)
        else:
            if self.node.subs.wrench_state.stage_1_closed:
                self.node.stop_control()
                self.node.set_state(FAILURE)
                err_msg = "Switch state inconsistent!"
                err_type = self.node.global_params['Reports']['Critical']['Hardware_malfunction']
                self.node.handle_internal_error(error_message=err_msg,
                                                error_type=err_type,
                                                event=self.node.global_params['events']['wrench_switch_failure'])
            else:
                if self.node.new_action_flag:
                    if json.loads(self.node.action.data)['action'] == 'close':
                        self.node.set_state(CLOSING)
                    self.node.new_action_flag = False


class ClosedState(AbstractState):
    def __init__(self, node):
        super(ClosedState, self).__init__(node, CLOSED)

    def on_transition_to(self):
        self.node._cur_action_seq = -1

    def do_work(self):
        self.node.stop_control()
        if not self.node.subs.wrench_state.stage_1_closed:
            self.node.set_state(CLOSING)
        else:
            if self.node.subs.wrench_state.stage_1_open:
                self.node.stop_control()
                self.node.set_state(FAILURE)
                err_msg = "Switch state inconsistent!"
                err_type = self.node.global_params['Reports']['Critical']['Hardware_malfunction']
                self.node.handle_internal_error(error_message=err_msg,
                                                error_type=err_type,
                                                event=self.node.global_params['events']['wrench_switch_failure'])
            else:
                if self.node.new_action_flag:
                    if json.loads(self.node.action.data)['action'] == 'open':
                        self.node.set_state(OPENING)
                    if json.loads(self.node.action.data)['action'] == 'turn':
                        self.node.set_state(TURN)
                    self.node.new_action_flag = False


class OpeningState(AbstractState):
    def __init__(self, node):
        super(OpeningState, self).__init__(node, OPENING)

    def do_work(self):
        if rospy.get_time() - max(self.node.get_change_state_timestamp(),
                                  self.node.last_empty_loop_ts) > self.node.node_params['opening_time']:
            self.node.handle_action_error('Could not open wrench', 'rc_open_wrench')

        self.node.wrench_control.move = -self.node.node_params['open_speed']
        if self.node.subs.wrench_state.stage_1_open:
            if self.node.subs.wrench_state.stage_1_closed:
                self.node.stop_control()
                self.node.set_state(FAILURE)
                err_msg = "Switch state inconsistent!"
                err_type = self.node.global_params['Reports']['Critical']['Hardware_malfunction']
                self.node.handle_internal_error(error_message=err_msg,
                                                error_type=err_type,
                                                event=self.node.global_params['events']['wrench_switch_failure'])
            else:
                self.node.stop_control()
                self.node.set_state(OPEN)


class ClosingState(AbstractState):
    def __init__(self, node):
        super(ClosingState, self).__init__(node, CLOSING)

    def do_work(self):
        if rospy.get_time() - max(self.node.get_change_state_timestamp(),
                                  self.node.last_empty_loop_ts) > self.node.node_params['closing_time']:
            self.node.handle_action_error('Could not close wrench', 'rc_close_wrench')
        self.node.wrench_control.move = self.node.node_params['close_speed']
        if self.node.subs.wrench_state.stage_1_closed:
            if self.node.subs.wrench_state.stage_1_open:
                self.node.stop_control()
                self.node.set_state(FAILURE)
                err_msg = "Switch state inconsistent!"
                err_type = self.node.global_params['Reports']['Critical']['Hardware_malfunction']
                self.node.handle_internal_error(error_message=err_msg,
                                                error_type=err_type,
                                                event=self.node.global_params['events']['wrench_switch_failure'])
            else:
                self.node.stop_control()
                self.node.set_state(CLOSED)


class TurnState(AbstractState):
    def __init__(self, node):
        super(TurnState, self).__init__(node, TURN)

    def do_work(self):
        if rospy.get_time() - max(self.node.get_change_state_timestamp(),
                                  self.node.last_empty_loop_ts) > self.node.node_params['turn_time']:
            self.node.handle_action_error('Could not turn wrench', 'rc_turn_wrench')
        self.node.wrench_control.grip = int(1.0)
        if self.node.subs.wrench_state.stage_2_closed:
            if self.node.subs.wrench_state.stage_2_open:
                self.node.stop_control()
                self.node.set_state(FAILURE)
                err_msg = "Switch state inconsistent!"
                err_type = self.node.global_params['Reports']['Critical']['Hardware_malfunction']
                self.node.handle_internal_error(error_message=err_msg,
                                                error_type=err_type,
                                                event=self.node.global_params['events']['wrench_switch_failure'])
            else:
                self.node.stop_control()
                self.node.set_state(RELEASE)


class ReleaseState(AbstractState):
    def __init__(self, node):
        super(ReleaseState, self).__init__(node, RELEASE)

    def do_work(self):
        if rospy.get_time() - max(self.node.get_change_state_timestamp(),
                                  self.node.last_empty_loop_ts) > self.node.node_params['release_time']:
            self.node.handle_action_error('Could not release wrench', 'rc_release_wrench')
        self.node.wrench_control.grip = int(-1.0)

        if self.node.subs.wrench_state.stage_2_open:
            if self.node.subs.wrench_state.stage_2_closed:
                self.node.stop_control()
                self.node.set_state(FAILURE)
                err_msg = "Switch state inconsistent!"
                err_type = self.node.global_params['Reports']['Critical']['Hardware_malfunction']
                self.node.handle_internal_error(error_message=err_msg,
                                                error_type=err_type,
                                                event=self.node.global_params['events']['wrench_switch_failure'])
            else:
                self.node.stop_control()
                self.node.set_state(CLOSED)
