#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import rosparam
import json
import numpy as np

from common.base_node import BaseNode
from common_msgs.msg import Action, MovePermission
from drill_msgs.msg import (StateMachineStatus, InternalError, ManualState, WrenchCtrlStamped, WrenchSwitchState,
                            VehicleBehaviorMode, FloatStamped)

# Состояния атомата

OPEN = "open"
CLOSING = "closing"
CLOSED = "closed"
TURN = "turn"
TURNED = "turned"
RELEASE = "release"
RELEASED = "released"
OPENING = "opening"

FAILURE = "failure"

SHAFT_BUILDUP = "shaft_buildup"
SHAFT_STOW = "shaft_stow"


class WrenchControllerNode(BaseNode):

    def __init__(self, name):
        super(WrenchControllerNode, self).__init__(name)

        self.__cur_action_seq = -1

        self.state = OPEN

        self.prev_state = None

        self.main_mode = None

        self.action = None
        self.new_action_flag = False

        self.move_permission = True

        self.wrench_control = WrenchCtrlStamped()

        self.wrench_state = None
        self.allow_turn_finish = False
        self.allow_release_finish = False

        # Флаг и время прихода устаревшего сообщения состяния концевиков
        self.is_old_state = False
        self.old_state_time = rospy.Time.now().to_sec()

        self.time_state_started = rospy.get_time()

        self.wrench_control_pub = rospy.Publisher(self.global_params['topics']['wrench_control'],
                                            WrenchCtrlStamped, queue_size=1)

        self.system_error_pub = rospy.Publisher(self.global_params['topics']['internal_error'],
                                                InternalError, queue_size=10)

        self.status_pub = rospy.Publisher(self.global_params['topics']['wrench_controller_status'],
                                          StateMachineStatus, queue_size=10)

        self.switches_map = {
            OPEN: self.switch_to_idle,
            CLOSED: self.switch_to_idle,
            TURNED: self.switch_to_idle,
            RELEASED: self.switch_to_idle,
            OPENING: self.switch_to_opening,
            CLOSING: self.switch_to_closing,
            TURN: self.switch_to_turn,
            RELEASE: self.switch_to_release,
            FAILURE: self.switch_to_failure,
        }

        self.handlers_map = {
            OPEN: self.handle_idle,
            CLOSED: self.handle_idle,
            TURNED: self.handle_idle,
            RELEASED: self.handle_idle,
            OPENING: self.handle_opening,
            CLOSING: self.handle_closing,
            TURN: self.handle_turn,
            RELEASE: self.handle_release,
            FAILURE: self.handle_failure,
        }

    def on_start(self):
        """
        Инициализация потоков подписчиков.
        """
        rospy.Subscriber(self.global_params['topics']['wrench_controller_action'],
                         Action, self.action_callback)

        rospy.Subscriber(self.global_params['topics']['main_mode'],
                         VehicleBehaviorMode, self.main_mode_callback)

        rospy.Subscriber(self.global_params['topics']['permission'],
                         MovePermission, self.permission_callback)

        rospy.Subscriber(self.global_params['topics']['manual_state'],
                         ManualState, self.manual_state_callback)

        rospy.Subscriber(self.global_params['topics']['wrench_state'],
                         WrenchSwitchState, self.wrench_state_callback)

    def do_work(self):
        # Если нет режима или состояния концевиков или задания - не работаем
        if (
                self.main_mode is None or
                self.wrench_state is None
                #self.action is None
        ):
            self.publish_status()
            return

        if self.main_mode != SHAFT_BUILDUP and self.main_mode != SHAFT_STOW:
            self.stop_control()
            self.publish_wrench_control()
            self.publish_status()
            return

        is_error = False

        if (rospy.Time.now().to_sec() - self.wrench_state.header.stamp.to_sec() >
                self.global_params['MSG_TTL']):
            err_msg = "Got outdated wrench switch state"
            self.handle_internal_error(error_message=err_msg)
            self.is_old_state = True
            self.old_state_time = rospy.Time.now().to_sec()
            is_error = True

        # Проверяем что есть разрешение работать
        if not self.move_permission:
            self.stop_control()
            is_error = True

        # Если ошибок нет - работаем
        if not is_error:
            handler = self.handlers_map.get(self.state)
            if handler is None:
                err_msg = "Unknown state: {}".format(self.state)
                self.handle_internal_error(error_message=err_msg)
                return
            handler()
        else:
            self.stop_control()

        self.publish_wrench_control()
        self.publish_status()

    def handle_idle(self):
        self.stop_control()
        if self.new_action_flag:
            self.switch_to_state(json.loads(self.action.data)['state'])
            self.new_action_flag = False

    def handle_opening(self):
        self.wrench_control.move = -self.node_params['open_speed']
        if self.wrench_state.stage_1_open:
            self.stop_control()
            self.switch_to_state(OPEN)

    def handle_closing(self):
        self.wrench_control.move = self.node_params['close_speed']
        if self.wrench_state.stage_1_closed:
            self.stop_control()
            self.switch_to_state(CLOSED)

    def handle_turn(self):
        self.wrench_control.grip = 1.0
        if not self.wrench_state.stage_2_closed:
            self.allow_turn_finish = True
        if self.allow_turn_finish and self.wrench_state.stage_2_closed:
            self.stop_control()
            self.switch_to_state(TURNED)
            self.allow_release_finish = False

    def handle_release(self):
        self.wrench_control.grip = -1.0
        if not self.wrench_state.stage_2_open:
            self.allow_release_finish = True
        if self.allow_release_finish and self.wrench_state.stage_2_open:
            self.stop_control()
            self.switch_to_state(RELEASED)
            self.allow_turn_finish = False


    def switch_to_state(self, state):
        self.log_state_change(to_state=state)
        self.stop_control()
        switch = self.switches_map.get(state, None)
        if switch is not None:
            switch()
            self.state = state
            self.time_state_started = rospy.get_time()
        else:
            self.log("WRONG STATE TO SWITCH! %s"%state)

    def handle_failure(self):
        """
        Хэндлер состояния FAILURE, который допускает возврат в предыдущее состояние при условии,
        что переход в FAILURE произошел в результате устаревшего стейта, и
        был получен новый свежий стейт, и
        еще не прошло максимальное допустимое время ожидания нового стейта.
        Перед возвратом в предыдущее сосотояние вызывается соответствующий этому состоянию
        метод switch_to_*, обновляющий данные для выполнения состояния.
        """
        self.stop_control()
        if (
                self.is_old_state and

                rospy.Time.now().to_sec() - self.old_state_time <
                self.global_params['new_state_TTL'] and

                rospy.Time.now().to_sec() - self.arm_switch_state.header.stamp.to_sec() <
                self.global_params['MSG_TTL']
        ):
            self.is_old_state = False
            self.switch_to_state(self.prev_state)

    def switch_to_idle(self):
        self.__cur_action_seq = -1

    def switch_to_opening(self):
        pass

    def switch_to_closing(self):
        pass

    def switch_to_turn(self):
        pass

    def switch_to_release(self):
        pass

    def switch_to_failure(self):
        self.stop_control()

    def publish_wrench_control(self):
        self.wrench_control.header.stamp = rospy.Time.now()
        self.wrench_control_pub.publish(self.wrench_control)

    def stop_control(self):
        self.wrench_control = WrenchCtrlStamped()

    def publish_status(self):
        message = StateMachineStatus()
        message.status = self.state
        message.cur_action_seq = self.__cur_action_seq
        message.header.stamp = rospy.Time.now()
        self.status_pub.publish(message)

    def handle_internal_error(self, error_message='', err_type=401):
        is_warning = err_type in self.global_params['Reports']['Warnings'].values()
        loglevel = 2 if is_warning else 3
        self.publish_alert_once(err_type, error_message, loglevel)

        if not is_warning:
            self.stop_control()
            if self.state != FAILURE:
                self.prev_state = self.state
                self.log_state_change(to_state=FAILURE)
                self.state = FAILURE

    def action_callback(self, message):
        try:
            read_json = json.loads(message.data)
            required_keys = (
                'state',
            )
            for k in required_keys:
                if k not in read_json:
                    err_descr = "Action data is incomplete or invalid"
                    self.handle_internal_error(error_message=err_descr)
                    return
            print read_json
            # Все ок - сохраняем задание
            if self.state in (CLOSED, OPEN, TURNED, RELEASED):
                self.action = message
                self.new_action_flag = True

                self.__cur_action_seq = message.seq
            else:
                self.log("Can't accept new action while not in one of final states", loglevel=2)

        except ValueError as e:
            err_descr = "Action data is not in valid json format"
            self.log(e, loglevel=3)
            self.handle_internal_error(error_message=err_descr)
            return

    def wrench_state_callback(self, message):
        self.wrench_state = message

    def permission_callback(self, message):
        self.move_permission = message.permission

    def main_mode_callback(self, message):
        self.main_mode = message.mode

    def log_state_change(self, to_state):
        self.log('Changing from {} to {}'.format(self.state.upper(), to_state.upper()))

    def manual_state_callback(self, message):
        if message.node_name == self._name:
            self.switch_to_state(message.mode)
