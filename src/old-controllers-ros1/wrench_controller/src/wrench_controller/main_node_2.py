#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import rosparam
import json

from .constants import OP<PERSON>, OPENING, CLOSING, CLOSED, TURN, RELEASE, SHAFT_BUILDUP, SHAFT_STOW
from state_machine_node import AbstractNodeStateMachine
from wrench_controller.models import OpenState, OpeningState, ClosedState, ClosingState, TurnState, ReleaseState

from common_msgs.msg import Action, MovePermission
from drill_msgs.msg import StateMachineStatus, InternalError, ManualState, WrenchCtrlStamped, WrenchSwitchState, VehicleBehaviorMode, FloatStamped


class WrenchControllerNode(AbstractNodeStateMachine):

    def drill_initialize(self, *args, **kwargs):

        self.set_current_state(OpenState(self))

        self.add_states([OpeningState(self), ClosedState(self),
                         ClosingState(self), TurnState(self), ReleaseState(self)])

        self.action = None
        self.new_action_flag = False

        self.wrench_control = WrenchCtrlStamped()

        # Флаг и время прихода устаревшего сообщения состяния концевиков
        self.is_old_state = False
        self.old_state_time = rospy.get_time()

        self.time_state_started = rospy.get_time()

        self.wrench_control_pub = rospy.Publisher(self.global_params['topics']['wrench_control'],
                                                  WrenchCtrlStamped, queue_size=1)

    def subscribers_initialize(self):
        self.add_subscriber(self.global_params['topics']['wrench_state'], WrenchSwitchState,
                            'wrench_state')

        self.add_subscriber(self.global_params['topics']['main_mode'], VehicleBehaviorMode,
                            'main_mode')

        rospy.Subscriber(self.global_params['topics']['wrench_controller_action'], Action,
                         self.action_callback)

    def publish_wrench_control(self):
        self.wrench_control.header.stamp = rospy.Time.now()
        self.wrench_control_pub.publish(self.wrench_control)

    def stop_control(self):
        self.wrench_control = WrenchCtrlStamped()

    def action_callback(self, message):
        try:
            read_json = json.loads(message.data)
            required_keys = (
                'action',
            )
            for k in required_keys:
                if k not in read_json:
                    err_descr = "Action data is incomplete or invalid"
                    self.handle_internal_error(error_message=err_descr)
                    return
            print(read_json)
            
            if self.get_current_state().get_name() in (CLOSED, OPEN):
                self.action = message
                self.new_action_flag = True
                self._cur_action_seq = message.seq
            else:
                self.log("Can't accept new action while not in one of final states", loglevel=2)

        except ValueError as e:
            err_descr = "Action data is not in valid json format"
            self.log(e, loglevel=3)
            self.handle_internal_error(error_message=err_descr)
            return

    def check_data(self):
        if self.subs.main_mode is None:
            return False

        invalid_modes = [
            VehicleBehaviorMode.REMOTE,
            VehicleBehaviorMode.REMOTE_WAIT,
            VehicleBehaviorMode.REMOTE_PREPARE
        ]

        return (self.subs.main_mode.mode in self.allowed_modes and
                self.subs.main_mode.mode not in invalid_modes)

    def do_work_after(self):
        self.publish_wrench_control()

    def handle_action_error(self, error_message, event_name):
        err_msg = error_message
        err_type = self.global_params['Reports']['Critical']['Action_failed']
        self.handle_internal_error(error_message=err_msg,
                                   error_type=err_type,
                                   event=self.global_params['events'][event_name])