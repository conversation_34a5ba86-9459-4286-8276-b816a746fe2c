#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import json
from threading import Lock

from .constants import IDLE, CLOSING, TURN_CW, TURN_CCW
from state_machine_node import AbstractNodeStateMachine
from carousel_controller.models import IdleState, ClosingState, TurnCwState, TurnCcwState

from common_msgs.msg import Action
from drill_msgs.msg import (CarouselCtrlStamped, CarouselSwitchState, VehicleBehaviorMode)


class CarouselControllerNode(AbstractNodeStateMachine):
    def drill_initialize(self, *args, **kwargs):
        # Set the current state to IdleState
        self.set_current_state(IdleState(self))

        # Add additional states to the state machine
        self.add_states([ClosingState(self), TurnCwState(self), TurnCcwState(self)])

        # Initialize variables
        self.carousel_control = CarouselCtrlStamped()
        self.carousel_action = None
        self.task_lock = Lock()

        # Create a publisher for carousel control commands
        self.carousel_control_pub = rospy.Publisher(
            self.global_params['topics']['carousel_control'], CarouselCtrlStamped, queue_size=1)

    def subscribers_initialize(self):
        # Subscribe to main mode topic
        self.add_subscriber(self.global_params['topics']['main_mode'], VehicleBehaviorMode, 'main_mode')

        # Subscribe to carousel switch state topic
        self.add_subscriber(self.global_params['topics']['carousel_switch_state'], CarouselSwitchState, 'carousel_state')

        # Subscribe to carousel action topic
        rospy.Subscriber(self.global_params['topics']['carousel_action'], Action, self.action_callback)

    def stop_control(self):
        # Stop carousel control by resetting carousel_control
        self.carousel_control = CarouselCtrlStamped()

    def publish_carousel_control(self):
        # Publish carousel control command
        self.carousel_control.header.stamp = rospy.Time.now()
        self.carousel_control_pub.publish(self.carousel_control)

    def action_callback(self, message):
        with self.task_lock:
            try:
                # Parse action message data as JSON
                read_json = json.loads(message.data)
                current_state_name = self.get_current_state().get_name()
                if current_state_name == IDLE:
                    if 'index' in read_json.keys():
                        self.carousel_action = read_json['index']
                    else:
                        err_descr = "Action data has no \"index\" field"
                        self.handle_internal_error(error_message=err_descr)
                        return
                    self._cur_action_seq = message.seq
                    self._last_action_seq = message.seq
                else:
                    self.log(
                        "Can't accept new action while not in IDLE state",
                        loglevel=2,
                        event=self.global_params['events']['wait_finish']
                    )
            except ValueError as e:
                err_descr = "Action data is not in valid json format"
                self.log(e, loglevel=3)
                self.handle_internal_error(error_message=err_descr)
                return

    def check_data(self):
        remote_modes = {VehicleBehaviorMode.REMOTE, VehicleBehaviorMode.REMOTE_WAIT, VehicleBehaviorMode.REMOTE_PREPARE}

        if not self.subs.main_mode:
            return False

        current_mode = self.subs.main_mode.mode

        if current_mode in self.allowed_modes:
            return True

        with self.task_lock:
            self.carousel_action = 0
            current_state_name = self.get_current_state().get_name()

            if current_state_name not in {IDLE, CLOSING} and current_mode not in remote_modes:
                self.set_state(CLOSING)

        return True

    def do_work_after(self):
        # Publish carousel control command after performing work
        self.publish_carousel_control()