#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import rosparam
import json
import numpy as np
from threading import Lock

from common.base_node import BaseNode
from common_msgs.msg import Action, MovePermission
from drill_msgs.msg import (StateMachineStatus, InternalError, ManualState, CarouselCtrlStamped, CarouselSwitchState,
                            VehicleBehaviorMode, FloatStamped)

# Состояния атомата

IDLE = "idle"
OPENING = "opening"
CLOSING = "closing"
TURN_CW = "turn_cw"
TURN_CCW = "turn_ccw"

FAILURE = "failure"

SHAFT_BUILDUP = "shaft_buildup"
SHAFT_STOW = "shaft_stow"
REMOTE = "remote"

OPEN = "open"
CLOSED = "closed"


class CarouselControllerNode(BaseNode):

    def __init__(self, name):
        super(CarouselControllerNode, self).__init__(name)

        self.__cur_action_seq = -1
        self.__last_action_seq = -1

        self.state = IDLE
        self.prev_state = IDLE

        self.main_mode = None

        self.action = None
        self.new_action_flag = False

        self.move_permission = True

        self.carousel_control = CarouselCtrlStamped()

        self.carousel_state = None

        self.wanted_state = CLOSED

        self.wanted_index = 1

        self.task_lock = Lock()

        # Флаг и время прихода устаревшего сообщения состяния концевиков
        self.is_old_state = False
        self.old_state_time = rospy.Time.now().to_sec()

        self.time_state_started = rospy.get_time()

        self.carousel_control_pub = rospy.Publisher(self.global_params['topics']['carousel_control'],
                                            CarouselCtrlStamped, queue_size=1)

        self.system_error_pub = rospy.Publisher(self.global_params['topics']['internal_error'],
                                                InternalError, queue_size=10)

        self.status_pub = rospy.Publisher(rospy.get_param("Global/topics/carousel_controller_status"),
                                          StateMachineStatus, queue_size=10)

        self.switches_map = {
            IDLE: self.switch_to_idle,
            OPENING: self.switch_to_opening,
            CLOSING: self.switch_to_closing,
            TURN_CW: self.switch_to_turn_cw,
            TURN_CCW: self.switch_to_turn_ccw,
            FAILURE: self.switch_to_failure,
        }

        self.handlers_map = {
            IDLE: self.handle_idle,
            OPENING: self.handle_opening,
            CLOSING: self.handle_closing,
            TURN_CW: self.handle_turn_cw,
            TURN_CCW: self.handle_turn_ccw,
            FAILURE: self.handle_failure,
        }

    def on_start(self):
        """
        Инициализация потоков подписчиков.
        """
        rospy.Subscriber(rospy.get_param("Global/topics/carousel_controller_action"),
                         Action, self.action_callback)

        rospy.Subscriber(self.global_params['topics']['main_mode'],
                         VehicleBehaviorMode, self.main_mode_callback)

        rospy.Subscriber(self.global_params['topics']['permission'],
                         MovePermission, self.permission_callback)

        rospy.Subscriber(self.global_params['topics']['manual_state'],
                         ManualState, self.manual_state_callback)

        rospy.Subscriber(self.global_params['topics']['carousel_switch_state'],
                         CarouselSwitchState, self.carousel_state_callback)

    def do_work(self):
        if (
                self.main_mode is None or
                self.carousel_state is None or
                self.main_mode == REMOTE
        ):
            self.publish_status()
            self.stop_control()
            self.publish_carousel_control()
            return

        if self.main_mode != SHAFT_BUILDUP and self.main_mode != SHAFT_STOW:
            with self.task_lock:
                self.wanted_state = CLOSED
                self.wanted_index = 1
                if self.state == OPENING:
                    self.switch_to_state(CLOSING)
                elif self.state == TURN_CCW:
                    self.switch_to_state(TURN_CW)

        is_error = False

        if (rospy.Time.now().to_sec() - self.carousel_state.header.stamp.to_sec() >
                self.global_params['MSG_TTL']):
            err_msg = "Got outdated carousel switch state"
            self.handle_internal_error(error_message=err_msg)
            self.is_old_state = True
            self.old_state_time = rospy.Time.now().to_sec()
            is_error = True

        # Проверяем что есть разрешение работать
        if not self.move_permission:
            self.stop_control()
            is_error = True

        # Если ошибок нет - работаем
        if not is_error:
            handler = self.handlers_map.get(self.state)
            if handler is None:
                err_msg = "Unknown state: {}".format(self.state)
                self.handle_internal_error(error_message=err_msg)
                return
            handler()
        else:
            self.stop_control()

        self.publish_carousel_control()
        self.publish_status()

    def handle_idle(self):
        self.stop_control()
        with self.task_lock:
            if self.wanted_state == OPEN and not self.carousel_state.open:
                self.switch_to_state(OPENING)
            elif self.wanted_state == CLOSED and not self.carousel_state.closed:
                self.switch_to_state(CLOSING)
            elif self.wanted_index == 1 and not self.carousel_state.index_1:
                self.switch_to_state(TURN_CW)
            elif self.wanted_index == 2 and not self.carousel_state.index_2:
                self.switch_to_state(TURN_CCW)
            else:
                self.__cur_action_seq = -1

    def handle_opening(self):
        self.carousel_control.move = self._params['open_speed']
        if self.carousel_state.open:
            self.stop_control()
            self.switch_to_state(IDLE)
        elif rospy.get_time() - self.time_state_started > self._params['max_oc_time']:
            err_descr = "Can't open carousel!"
            err_type = self.global_params['Reports']['Critical']['Action_failed']
            self.handle_internal_error(error_message=err_descr, err_type=err_type)

    def handle_closing(self):
        self.carousel_control.move = -self._params['close_speed']
        if self.carousel_state.closed:
            self.stop_control()
            self.switch_to_state(IDLE)
        elif rospy.get_time() - self.time_state_started > self._params['max_oc_time']:
            err_descr = "Can't close carousel!"
            err_type = self.global_params['Reports']['Critical']['Action_failed']
            self.handle_internal_error(error_message=err_descr, err_type=err_type)

    def handle_turn_cw(self):
        self.carousel_control.rotate = -self._params['rotate_speed']
        if self.carousel_state.index_1:
            self.stop_control()
            self.switch_to_state(IDLE)
        elif rospy.get_time() - self.time_state_started > self._params['max_turn_time']:
            err_descr = "Can't rotate carousel to index 1!"
            err_type = self.global_params['Reports']['Critical']['Action_failed']
            self.handle_internal_error(error_message=err_descr, err_type=err_type)

    def handle_turn_ccw(self):
        self.carousel_control.rotate = self._params['rotate_speed']
        if self.carousel_state.index_2:
            self.stop_control()
            self.switch_to_state(IDLE)
        elif rospy.get_time() - self.time_state_started > self._params['max_turn_time']:
            err_descr = "Can't rotate carousel to index 2!"
            err_type = self.global_params['Reports']['Critical']['Action_failed']
            self.handle_internal_error(error_message=err_descr, err_type=err_type)

    def switch_to_state(self, state):
        self.log_state_change(to_state=state)
        self.stop_control()
        switch = self.switches_map.get(state, None)
        if switch is not None:
            switch()
            self.state = state
            self.time_state_started = rospy.get_time()
        else:
            self.log("WRONG STATE TO SWITCH! %s"%state)

    def handle_failure(self):
        """
        Хэндлер состояния FAILURE, который допускает возврат в предыдущее состояние при условии,
        что переход в FAILURE произошел в результате устаревшего стейта, и
        был получен новый свежий стейт, и
        еще не прошло максимальное допустимое время ожидания нового стейта.
        Перед возвратом в предыдущее сосотояние вызывается соответствующий этому состоянию
        метод switch_to_*, обновляющий данные для выполнения состояния.
        """
        self.stop_control()
        if (
                self.is_old_state and

                rospy.Time.now().to_sec() - self.old_state_time <
                rospy.get_param('Global/new_state_TTL') and

                rospy.Time.now().to_sec() - self.carousel_state.header.stamp.to_sec() <
                self.global_params['MSG_TTL']
        ):
            self.is_old_state = False
            self.switch_to_state(self.prev_state)

    def switch_to_idle(self):
        self.__cur_action_seq = -1

    def switch_to_opening(self):
        pass

    def switch_to_closing(self):
        pass

    def switch_to_turn_cw(self):
        pass

    def switch_to_turn_ccw(self):
        pass

    def switch_to_failure(self):
        self.stop_control()

    def publish_carousel_control(self):
        self.carousel_control.header.stamp = rospy.Time.now()
        self.carousel_control_pub.publish(self.carousel_control)

    def stop_control(self):
        self.carousel_control = CarouselCtrlStamped()

    def publish_status(self):
        message = StateMachineStatus()
        message.status = self.state
        message.cur_action_seq = self.__cur_action_seq
        message.last_action_seq = self.__last_action_seq
        message.header.stamp = rospy.Time.now()
        self.status_pub.publish(message)

    def handle_internal_error(self, error_message='', err_type=401):
        is_warning = err_type in self.global_params['Reports']['Warnings'].values()
        loglevel = 2 if is_warning else 3
        self.publish_alert_once(err_type, error_message, loglevel)

        if not is_warning:
            self.stop_control()
            if self.state != FAILURE:
                self.prev_state = self.state
                self.log_state_change(to_state=FAILURE)
                self.state = FAILURE

    def action_callback(self, message):
        with self.task_lock:
            try:
                read_json = json.loads(message.data)
                if self.state == IDLE:
                    if 'state' in read_json.keys():
                        self.wanted_state = read_json['state']
                    if 'index' in read_json.keys():
                        self.wanted_index = read_json['index']
                    self.__cur_action_seq = message.seq
                    self.__last_action_seq = message.seq
                else:
                    self.log("Can't accept new action while not in IDLE", loglevel=2)

            except ValueError as e:
                err_descr = "Action data is not in valid json format"
                self.log(e, loglevel=3)
                self.handle_internal_error(error_message=err_descr)
                return

    def carousel_state_callback(self, message):
        self.carousel_state = message

    def permission_callback(self, message):
        self.move_permission = message.permission

    def main_mode_callback(self, message):
        self.main_mode = message.mode

    def log_state_change(self, to_state):
        self.log('Changing from {} to {}'.format(self.state.upper(), to_state.upper()))

    def manual_state_callback(self, message):
        if message.node_name == self._name:
            if message.mode == "prev":
                if self.state == FAILURE:
                    self.switch_to_state(self.prev_state)
            else:
                self.switch_to_state(message.mode)
