#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import json
import math
import numpy as np

from common.base_node import BaseNode
from common_msgs.msg import MovePermission, MovingError, Action
from drill_msgs.msg import (StateMachineStatus, InternalError, VehicleBehaviorMode,
                            ManualState, TowerSwitchState, TowerCtrlStamped, TowerAngle,
                            FloatStamped, ArmState, BoolStamped)

# Состояния автомата
IDLE = 'idle'
LOCKING = 'locking'
UNLOCKING = 'unlocking'
ARM_CLOSING = 'arm_closing'
TILT_REGULATION = 'tilt_regulation'
FAILURE = 'failure'
# Статусы (состояние автомата) люнета
CLOSED = 'closed'


class TowerControllerNode(BaseNode):

    def __init__(self, name):
        super(TowerControllerNode, self).__init__(name)

        self.move_permission = True
        self.allow_perm_log = True
        # Режим приходящий сверху от главного автомата
        self.main_mode = None

        # Текущее состояние автомата, стартовое - 'idle'
        self.state = IDLE
        # Время начала исполнения нового состояния автомата
        self.time_state_started = None
        # Предыдущее сосотояние будет сохраняться для возврата в него из 'failure'
        self.prev_state = IDLE

        # Полученое задание
        self.action = None
        self.new_action_flag = False

        # Последнее сообщение о состоянии мачты, сохраненное для проверки на свежесть
        self.tower_state = None
        self.switch_state = None

        # Флаг ошибки движения (неверная/отсутствующая реакция на управляющее воздействие)
        self.move_error = False

        # Флаг получения устаревшего стейта (для выхода из failure с случае получения свежего)
        self.old_tower_state = False
        # Время получения устаревшего стейта
        self.old_state_time = None

        # Время в течении которого удерживался достигнутый целевой угол
        self.time_tilt_fixed_started = 0

        # Номер текущего экшена
        self.__cur_action_seq = -1
        # Номер последнего экшена
        self.__last_action_seq = -1

        self.vertical_pin_movement = 0
        self.inclined_pin_movement = 0

        # Скорость наклона
        self.tilt_speed = 0

        # Флаги наличия сигналов от концевиков.
        self.vertical_switch_on = False
        self.inclined_switch_on = False

        # Предыдущий и текущий угол наклона
        self.prev_tilt = None
        self.current_tilt = None
        # Целевой угол из задания
        self.target_tilt = None

        self.arm_status = None

        self.allow_move_perm_log = True

        self.tower_ctrl_pub = rospy.Publisher(self.global_params['topics']['tower_control'],
                                              TowerCtrlStamped, queue_size=10)

        self.tower_controller_status_pub = rospy.Publisher(self.global_params['topics']['arm_controller_status'],
                                                           StateMachineStatus, queue_size=10)

        self.arm_action_pub = rospy.Publisher(self.global_params['topics']['arm_action'],
                                              Action, queue_size=10)

        self.system_error_pub = rospy.Publisher(self.global_params['topics']['internal_error'],
                                                InternalError, queue_size=10)

        self.moving_error_pub = rospy.Publisher(self.global_params['topics']['moving_error'],
                                                MovingError, queue_size=10)

        self.angle_rtl_pub = rospy.Publisher("/tower_angle_ready", BoolStamped, queue_size=10)

        self.arm_present = self.global_params['system_flags']['arm_present']

    def on_start(self):
        """
        Инициализация потоков подписчиков.
        """
        rospy.Subscriber(self.global_params['topics']['tower_action'],
                         Action, self.action_callback)

        rospy.Subscriber(self.global_params['topics']['tower_state'],
                         TowerAngle, self.tower_state_callback)

        rospy.Subscriber(self.global_params['topics']['tower_switch_state'],
                         TowerSwitchState, self.tower_switch_state_callback)

        rospy.Subscriber(self.global_params['topics']['main_mode'],
                         VehicleBehaviorMode, self.main_mode_callback)

        rospy.Subscriber(self.global_params['topics']['permission'],
                         MovePermission, self.permission_callback)

        rospy.Subscriber(self.global_params['topics']['manual_state'],
                         ManualState, self.manual_state_callback)

        rospy.Subscriber(self.global_params['topics']['arm_controller_status'],
                         StateMachineStatus, self.arm_status_callback)

    def do_work(self):
        handlers_map = {
            IDLE: self.handle_idle,
            LOCKING: self.handle_locking,
            UNLOCKING: self.handle_unlocking,
            ARM_CLOSING: self.handle_arm_closing,
            TILT_REGULATION: self.handle_tilt_regulation,
            FAILURE: self.handle_failure,
        }
        # Если нет режима или состояния мачты или задания - не работаем
        if not self.got_needed_data():
            self.publish_status()
            return

        is_error = False
        states_msgs = (
            self.switch_state,
            self.tower_state,
            self.arm_status,
        )
        # Проверяем, что текущий режим позвляет работать, если нет - останавливаем работу
        if self.new_action_flag and self.main_mode not in self._params['allowed_modes']:
            err_msg = "Main mode {} does't allow tower regulation".format(self.main_mode)
            self.handle_internal_error(error_message=err_msg)
            is_error = True

        # Проверяем что есть разрешение работать
        if not self.move_permission:
            err_msg = "No permission for moving"
            err_num = MovingError().BLOCKED_BY_PERMISSION
            self.handle_moving_error(err_msg, err_num=err_num)
            is_error = True

        # Проверяем что последний полученый стейт не слишком старый
        for state in states_msgs:
            if rospy.Time.now().to_sec() - state.header.stamp.to_sec() > self.global_params['MSG_TTL']:
                err_msg = "Got outdated state"
                self.handle_internal_error(error_message=err_msg)
                self.old_tower_state = True
                self.old_state_time = rospy.Time.now().to_sec()
                is_error = True

        if not self.vertical_switch_on:
            err_msg = "Vertical pin unlocked!"
            self.handle_internal_error(error_message=err_msg)
            is_error = True

        reached  = False
        for angle in self._params['allowed_angles'].values():
            reached = abs(angle - self.current_tilt) <= self._params["allowed_tilt_error"]
            if reached:
                break

        msg = BoolStamped()

        msg.header.stamp = rospy.get_rostime()
        msg.value = reached

        self.angle_rtl_pub.publish(msg)


        # Если ошибок нет - работаем
        if not is_error:
            handler = handlers_map.get(self.state)
            if handler is None:
                err_msg = "Unknown state: {}".format(self.state)
                self.handle_internal_error(error_message=err_msg)
                self.publish_status()
                return
            handler()
        else:
            self.stop_control()

        # Публикуем скорости
        self.publish_tower_control()

        # Публикуем текущий статус контоллера наклона
        self.publish_status()

    def got_needed_data(self):
        """
        Проверяет получены ли необходимые для работы данные
        """
        needed_data = (
            self.main_mode,
            self.tower_state,
            self.switch_state,
            self.arm_status
        )
        if all(needed_data):
            return True
        return False

    def handle_idle(self):
        if self.new_action_flag:
            # Обновляем номер последнего и текущего задания
            self.__last_action_seq = self.action.seq
            self.__cur_action_seq = self.action.seq

            if self.current_tilt > -180 and abs(self.current_tilt - self.target_tilt) >= self._params['min_angle_delta']:
                if self.current_tilt > self._params['min_angle_delta'] or self.target_tilt > self._params['min_angle_delta']:
                    self.switch_to_arm_closing()
                else:
                    self.switch_to_unlocking()
            elif self.current_tilt <= -180:
                self.log("No valid feedback, assume the angle is correct", loglevel=2)
                self.__cur_action_seq = -1
                self.new_action_flag = False
            else:
                self.log("Target angle is the same as the current, do nothing", loglevel=1)
                self.__cur_action_seq = -1
                self.new_action_flag = False

    def handle_unlocking(self):
        self.do_unlocking()

        # Если пропал сигнал с концевика и прошло достаточно времени после
        if not self.inclined_switch_on and \
                rospy.Time.now().to_sec() - self.time_state_started >= self._params["pins_move_time"]:
            self.switch_to_tilt_regulation()

        # Если прошло достаточно времени, но сигналы с концевиков так и не пропали - переходим в ошибку
        elif self.inclined_switch_on and \
                rospy.Time.now().to_sec() - self.time_state_started >= self._params["pins_no_reaction_time"]:

            err_msg = "Could not unlock tower"
            err_type = self.global_params['Reports']['Critical']['Action_failed']
            self.handle_internal_error(error_message=err_msg, err_type=err_type)

    def handle_arm_closing(self):
        if self.arm_status.status == CLOSED or not self.arm_present:
            self.switch_to_unlocking()

    def handle_tilt_regulation(self):
        self.do_tilt_regulation()

        # TODO: добавить хэндлер ошибки движения
        # Если нет реакции на управляющее воздействие - переходим в ошибку
        if self.move_error:
            err_msg = "No response to the control input"
            err_type = self.global_params['Reports']['Critical']['Action_failed']
            self.handle_internal_error(error_message=err_msg, err_type=err_type)

        # Если фиксация мачты занимает слишком много времени - переходим в ошибку
        if rospy.Time.now().to_sec() - self.time_state_started >= self._params["tilt_regulation_time"]:
            err_msg = "Could not fix tilt"
            err_type = self.global_params['Reports']['Critical']['Action_failed']
            self.handle_internal_error(error_message=err_msg, err_type=err_type)

        reached = abs(self.target_tilt - self.current_tilt) <= self._params["allowed_tilt_error"]

        if not reached:
            self.time_tilt_fixed_started = rospy.get_time()
        else:
            self.stop_control()
            if rospy.get_time() - self.time_tilt_fixed_started > self._params['tilt_fixed_time']:
                self.switch_to_locking()

    def handle_locking(self):
        self.do_locking()

        if not self.inclined_switch_on and \
                rospy.Time.now().to_sec() - self.time_state_started >= self._params["pins_move_time"]:

            err_msg = "Could not lock tower"
            err_type = self.global_params['Reports']['Critical']['Action_failed']
            self.handle_internal_error(error_message=err_msg, err_type=err_type)

        elif self.inclined_switch_on:
            self.inclined_pin_movement = 0
            self.switch_to_idle()

    def handle_failure(self):
        """
        Хэндлер состояния FAILURE, который допускает возврат в предыдущее состояние при условии,
        что переход в FAILURE произошел в результате устаревшего стейта, и
        был получен новый свежий стейт, и
        еще не прошло максимальное допустимое время ожидания нового стейта.
        Перед возвратом в предыдущее сосотояние вызывается соответствующий этому состоянию
        метод switch_to_*, обновляющий данные для выполнения состояния.
        """
        switches_map = {
            IDLE: self.switch_to_idle,
            UNLOCKING: self.switch_to_unlocking,
            LOCKING: self.switch_to_locking,
            TILT_REGULATION: self.switch_to_tilt_regulation,
        }
        if (
                self.old_tower_state and
                rospy.Time.now().to_sec() - self.old_state_time < self.global_params['new_state_TTL'] and
                rospy.Time.now().to_sec() - self.tower_state.header.stamp.to_sec() < self.global_params['MSG_TTL']
        ):
            self.old_tower_state = False
            switch = switches_map.get(self.prev_state, None)
            if switch is not None:
                switch()

    def switch_to_idle(self):
        self.log_state_change(IDLE)
        self.state = IDLE
        self.time_state_started = rospy.get_time()
        self.__cur_action_seq = -1
        self.new_action_flag = False

    def switch_to_unlocking(self):
        self.log_state_change(UNLOCKING)
        self.state = UNLOCKING
        self.time_state_started = rospy.get_time()

    def switch_to_locking(self):
        self.log_state_change(LOCKING)
        self.state = LOCKING
        self.time_state_started = rospy.get_time()

    def switch_to_tilt_regulation(self):
        self.log_state_change(TILT_REGULATION)
        self.state = TILT_REGULATION
        self.time_state_started = rospy.get_time()
        self.time_tilt_fixed_started = rospy.get_time()

    def switch_to_arm_closing(self):
        self.log_state_change(ARM_CLOSING)
        self.state = ARM_CLOSING
        self.time_state_started = rospy.get_time()
        if self.arm_present:
            self.publish_arm_action(do_open=False)

    def do_tilt_regulation(self):
        o = (self.target_tilt - self.current_tilt) * self._params["tilt_error2speed"]
        if abs(o) > 1:
            o = 1*np.sign(o)
        self.tilt_speed = o

        self.vertical_pin_movement = 0
        self.inclined_pin_movement = 0

    def do_locking(self):
        self.tilt_speed = 0
        self.inclined_pin_movement = 1
        self.vertical_pin_movement = 0


    def do_unlocking(self):
        self.tilt_speed = 0
        self.inclined_pin_movement = -1
        self.vertical_pin_movement = 0


    def publish_tower_control(self):
        message = TowerCtrlStamped()
        message.header.stamp = rospy.get_rostime()

        message.vertical_pin_movement = 0
        message.inclined_pin_movement = self.inclined_pin_movement
        message.tilt_speed = self.tilt_speed

        self.tower_ctrl_pub.publish(message)

    def publish_status(self):
        message = StateMachineStatus()
        message.status = self.state
        message.last_action_seq = self.__last_action_seq
        message.cur_action_seq = self.__cur_action_seq
        message.header.stamp = rospy.Time.now()

        self.tower_controller_status_pub.publish(message)

    def publish_arm_action(self, do_open=False):
        message = Action()
        message.data = json.dumps({'open': do_open})
        message.seq = 1
        self.arm_action_pub.publish(message)

    def handle_internal_error(self, error_message='', err_type=401):
        is_warning = err_type in self.global_params['Reports']['Warnings'].values()
        loglevel = 2 if is_warning else 3
        self.publish_alert_once(err_type, error_message, loglevel)

        if not is_warning:
            self.stop_control()

            if self.state != FAILURE:
                self.log_state_change(FAILURE)
                self.state = FAILURE

    def handle_moving_error(self, error_message, err_num, warning=False):
        self.stop_control()

        loglevel = 2 if warning else 3
        if self.allow_move_perm_log or err_num != MovingError.BLOCKED_BY_PERMISSION:
            self.log(error_message, loglevel=loglevel)
            if err_num == MovingError.BLOCKED_BY_PERMISSION:
                self.allow_move_perm_log = False

        moving_error = MovingError()
        moving_error.err_num = err_num

        self.moving_error_pub.publish(moving_error)

    def stop_control(self):
        self.tilt_speed = 0
        self.vertical_pin_movement = 0
        self.inclined_pin_movement = 0

    def tower_state_callback(self, message):
        self.tower_state = message
        self.current_tilt = message.value

    def tower_switch_state_callback(self, message):
        self.switch_state = message
        self.vertical_switch_on = message.vertical_switch_on
        self.inclined_switch_on = message.inclined_switch_on

    def action_callback(self, message):
        if message.seq > self.__last_action_seq:
            try:
                read_json = json.loads(message.data)
                required_keys = (
                    'tower_angle',
                )
                for k in required_keys:
                    if k not in read_json:
                        err_descr = "Action data is incomplete or invalid"
                        self.handle_internal_error(error_message=err_descr)
                        return

                target_tilt = read_json['tower_angle']
                if str(int(target_tilt)) not in self._params['allowed_angles'].keys():
                    err_descr = "Target tilt ({} degrees) from action is not allowed".format(target_tilt)

                    self.handle_internal_error(error_message=err_descr)
                    return
                if target_tilt != 0 and self.current_tilt < -180 and not self._params['allow_inclined_with_no_feedback']:
                    err_msg = "Can't drill inclined hole - tower tilt feedback is invalid!"
                    err_type = self.global_params['Reports']['Critical']['Action_failed']
                    self.handle_internal_error(error_message=err_msg, err_type=err_type)
                    return
                elif self.current_tilt < -180:
                    self.log("Tower tilt feedback is invalid, ensure that tower is in correct position!", loglevel=2)
                self.target_tilt = self._params['allowed_angles'][str(int(target_tilt))]
                # Все ок - сохраняем экшен
                self.action = message
                self.new_action_flag = True

            except ValueError:
                err_descr = "Action data is not in valid json format"
                self.handle_internal_error(error_message=err_descr)
        else:
            err_descr = "Action.seq is not set or invalid"
            self.handle_internal_error(error_message=err_descr)

    def main_mode_callback(self, message):
        self.main_mode = message.mode

    def permission_callback(self, message):
        self.move_permission = message.permission
        if not self.move_permission and message.permission:
            self.time_state_started = rospy.get_time()
        if not message.permission:
            self.allow_perm_log = True

    def manual_state_callback(self, message):
        if message.node_name == self._name:
            if message.mode == "prev":
                if self.state == FAILURE:
                    self.log_state_change(self.prev_state)
                    self.state = self.prev_state
                    self.time_state_started = rospy.get_time()
                    self.time_tilt_fixed_started = rospy.get_time()
            else:
                self.log_state_change(message.mode)
                self.state = message.mode
                self.time_state_started = rospy.get_time()
                self.time_tilt_fixed_started = rospy.get_time()

    def arm_status_callback(self, message):
        self.arm_status = message

    def log_state_change(self, to_state):
        """
        :param to_state: Состояние, в которое делается переход из текущего.
        """
        if self.state != FAILURE:
            self.prev_state = self.state
        self.log('Changing from {} to {}'.format(self.state.upper(), to_state.upper()))
