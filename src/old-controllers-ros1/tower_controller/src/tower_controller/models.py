#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy

from .constants import ID<PERSON>, LOCKING, UNLOCKING, ARM_CLOSING, TILT_REGULATION, CLOSED
from state_machine_node import AbstractState


class IdleState(AbstractState):
    def __init__(self, node):
        super(IdleState, self).__init__(node, IDLE)

    def on_transition_to(self):
        if self.node.action is not None:
            self.node._cur_action_seq = self.node.action.seq
            self.node._last_action_seq = self.node.action.seq
        else:
            self.node._cur_action_seq = -1
            self.node._last_action_seq = -1

    def do_work(self):
        if not self.node.new_action_flag:
            return
        if self.node.subs.tower_state.value > -180 and \
                abs(self.node.subs.tower_state.value - self.node.target_tilt) >= self.node.min_angle_delta:
            if self.node.subs.tower_state.value > self.node.min_angle_delta or self.node.target_tilt > self.node.min_angle_delta:
                self.node.set_state(ARM_CLOSING)
            else:
                self.node.set_state(UNLOCKING)
        elif self.node.subs.tower_state.value <= -180:
            self.node.log("No valid feedback, assume the angle is correct", loglevel=2)
            self.node._cur_action_seq = -1
            self.node.new_action_flag = False
        else:
            self.node.log("Target angle is the same as the current, do nothing", loglevel=1)
            self.node._cur_action_seq = -1
            self.node.new_action_flag = False
            


class LockingState(AbstractState):
    def __init__(self, node):
        super(LockingState, self).__init__(node, LOCKING)

    def do_work(self):
        self.node.do_locking()
        if not self.node.subs.tower_switch_state.inclined_switch_on and \
                rospy.get_time() - self.node.get_change_state_timestamp() >= \
                self.node.pins_move_time:
            err_msg = "Could not lock tower"
            err_type = self.node.global_params['Reports']['Critical']['Action_failed']
            self.node.handle_internal_error(error_message=err_msg,
                                            err_type=err_type,
                                            event=self.node.global_params['events']['rc_lock_tower'])
        elif self.node.subs.tower_switch_state.inclined_switch_on:
            self.node.inclined_pin_movement = 0
            self.node.set_state(IDLE)


class UnlockingState(AbstractState):
    def __init__(self, node):
        super(UnlockingState, self).__init__(node, UNLOCKING)

    def do_work(self):
        self.node.do_unlocking()
        # Если пропал сигнал с концевика и прошло достаточно времени после
        if not self.node.subs.tower_switch_state.inclined_switch_on and \
                rospy.get_time() - self.node.get_change_state_timestamp() >= \
                self.node.pins_move_time:
            self.node.set_state(TILT_REGULATION)

        # Если прошло достаточно времени, но сигналы с концевиков так и не пропали - переходим в ошибку
        elif self.node.subs.tower_switch_state.inclined_switch_on and \
                rospy.get_time() - self.node.get_change_state_timestamp() >= \
                self.node.pins_no_reaction_time:
            err_msg = "Could not unlock tower"
            err_type = self.node.global_params['Reports']['Critical']['Action_failed']
            self.node.handle_internal_error(error_message=err_msg,
                                            err_type=err_type,
                                            event=self.node.global_params['events']['rc_unlock_tower'])


class ArmClosingState(AbstractState):
    def __init__(self, node):
        super(ArmClosingState, self).__init__(node, ARM_CLOSING)

    def on_transition_to(self):
        if self.node.arm_present:
            self.node.publish_arm_action(do_open=False)

    def do_work(self):
        if self.node.subs.arm_status.status == CLOSED or not self.node.arm_present:
            self.node.set_state(UNLOCKING)


class TiltRegulationState(AbstractState):
    def __init__(self, node):
        super(TiltRegulationState, self).__init__(node, TILT_REGULATION)

    def on_transition_to(self):
        self.node.time_tilt_fixed_started = rospy.get_time()

    def do_work(self):
        self.do_tilt_regulation()

        # Если фиксация мачты занимает слишком много времени - переходим в ошибку
        if rospy.get_time() - self.node.get_change_state_timestamp() >= \
                self.node.tilt_regulation_time:
            err_msg = "Could not fix tilt"
            err_type = self.node.global_params['Reports']['Critical']['Action_failed']
            self.node.handle_internal_error(error_message=err_msg,
                                            err_type=err_type,
                                            event=self.node.global_params['events']['rc_tower_tilt'])

        reached = abs(self.node.target_tilt - self.node.subs.tower_state.value) <= self.node.allowed_tilt_error

        if not reached:
            self.node.time_tilt_fixed_started = rospy.get_time()
        else:
            self.node.stop_control()
            if rospy.get_time() - self.node.time_tilt_fixed_started > self.node.tilt_fixed_time:
                self.node.set_state(LOCKING)
