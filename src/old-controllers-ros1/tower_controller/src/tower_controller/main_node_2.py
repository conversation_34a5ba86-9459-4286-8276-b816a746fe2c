#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import json
import numpy as np

from tower_controller.models import IdleState, LockingState, UnlockingState, ArmClosingState, \
    TiltRegulationState
from state_machine_node import AbstractNodeStateMachine

from common_msgs.msg import MovePermission, MovingError, Action
from drill_msgs.msg import (StateMachineStatus, InternalError, VehicleBehaviorMode,
                            ManualState, TowerSwitchState, TowerCtrlStamped, TowerAngle,
                            FloatStamped, ArmState, BoolStamped)


class TowerControllerNode(AbstractNodeStateMachine):

    def drill_initialize(self, *args, **kwargs):

        self.set_current_state(IdleState(self))

        self.add_states([LockingState(self), UnlockingState(self), ArmClosingState(self),
                         TiltRegulationState(self)])

        self.vertical_pin_movement = 0
        self.inclined_pin_movement = 0

        # Скорость наклона
        self.tilt_speed = 0

        # Предыдущий и текущий угол наклона
        self.prev_tilt = None
        self.current_tilt = None

        # Целевой угол из задания
        self.target_tilt = None

        self.action = None
        self.new_action_flag = False

        self.tower_ctrl_pub = rospy.Publisher(self.global_params['topics']['tower_control'],
                                              TowerCtrlStamped, queue_size=10)

        self.arm_action_pub = rospy.Publisher(self.global_params['topics']['arm_action'],
                                              Action, queue_size=10)

        self.angle_rtl_pub = rospy.Publisher("/tower_angle_ready", BoolStamped, queue_size=10)

        self.arm_present = self.global_params['system_flags']['arm_present']

    def subscribers_initialize(self):

        self.add_subscriber(self.global_params['topics']['arm_controller_status'],
                            StateMachineStatus, 'arm_status')

        self.add_subscriber(self.global_params['topics']['tower_switch_state'],
                            TowerSwitchState, 'switch_state')

        self.add_subscriber(self.global_params['topics']['tower_state'],
                            TowerAngle, 'tower_state')

        self.add_subscriber(self.global_params['topics']['main_mode'],
                            VehicleBehaviorMode, 'main_mode')

        rospy.Subscriber(self.global_params['topics']['tower_action'],
                         Action, self.action_callback)

    def do_tilt_regulation(self):
        o = (self.target_tilt - self.subs.tower_state.value) * self.tilt_error2speed
        if abs(o) > 1:
            o = 1*np.sign(o)
        self.tilt_speed = o
        self.vertical_pin_movement = 0
        self.inclined_pin_movement = 0

    def do_locking(self):
        self.tilt_speed = 0
        self.inclined_pin_movement = 1
        self.vertical_pin_movement = 0

    def do_unlocking(self):
        self.tilt_speed = 0
        self.inclined_pin_movement = -1
        self.vertical_pin_movement = 0

    def stop_control(self):
        self.tilt_speed = 0
        self.vertical_pin_movement = 0
        self.inclined_pin_movement = 0

    def publish_tower_control(self):
        message = TowerCtrlStamped()
        message.header.stamp = rospy.Time.now()
        message.vertical_pin_movement = 0
        message.inclined_pin_movement = self.inclined_pin_movement
        message.tilt_speed = self.tilt_speed
        self.tower_ctrl_pub.publish(message)

    def publish_arm_action(self, do_open=False):
        message = Action()
        message.data = json.dumps({'open': do_open})
        message.seq = 1
        self.arm_action_pub.publish(message)

    def action_callback(self, message):
        if message.seq > self._last_action_seq:
            try:
                read_json = json.loads(message.data)
                required_keys = (
                    'tower_angle',
                )
                for k in required_keys:
                    if k not in read_json:
                        err_descr = "Action data is incomplete or invalid"
                        self.handle_internal_error(error_message=err_descr)
                        return

                target_tilt = read_json['tower_angle']
                if str(int(target_tilt)) not in self.allowed_angles.keys():
                    err_descr = "Target tilt ({} degrees) from action is not allowed".\
                        format(target_tilt)
                    self.handle_internal_error(error_message=err_descr)
                    return
                if target_tilt != 0 and self.subs.tower_state.value < -180 and \
                        not self.allow_inclined_with_no_feedback:
                    err_msg = "Can't drill inclined hole - tower tilt feedback is invalid!"
                    err_type = self.global_params['Reports']['Critical']['Action_failed']
                    self.handle_internal_error(error_message=err_msg, err_type=err_type)
                    return
                elif self.subs.tower_state.value < -180:
                    self.log(
                        "Tower tilt feedback is invalid, ensure that tower is in correct position!",
                        loglevel=2)
                self.target_tilt = self.allowed_angles[str(int(target_tilt))]
                # Все ок - сохраняем экшен
                self.action = message
                self.new_action_flag = True
                self._last_action_seq = message.seq

            except ValueError:
                err_descr = "Action data is not in valid json format"
                self.handle_internal_error(error_message=err_descr)
        else:
            err_descr = "Action.seq is not set or invalid"
            self.handle_internal_error(error_message=err_descr)

    def check_data(self):
        if not (self.subs.main_mode and self.subs.tower_state and
                self.subs.switch_state and self.subs.arm_status):
            return False

        is_error = False

        # Проверяем, что текущий режим позвляет работать, если нет - останавливаем работу
        if self.new_action_flag and self.subs.main_mode.mode not in self.allowed_modes:
            err_msg = "Main mode {} does't allow tower regulation".format(self.subs.main_mode.mode)
            self.handle_internal_error(error_message=err_msg)
            is_error = True

        if not self.subs.switch_state.vertical_switch_on:
            err_msg = "Vertical pin unlocked!"
            self.handle_internal_error(error_message=err_msg,
                                       event=self.global_params['events']['rc_lock_tower'])
            is_error = True

        return not is_error

    def do_work_after(self):
        reached = False
        if self.subs.tower_state is not None:
            for angle in self.allowed_angles.values():
                reached = abs(angle - self.subs.tower_state.value) <= self.allowed_tilt_error
                if reached:
                    break

        msg = BoolStamped()
        msg.header.stamp = rospy.get_rostime()
        msg.value = reached

        self.angle_rtl_pub.publish(msg)
        self.publish_tower_control()