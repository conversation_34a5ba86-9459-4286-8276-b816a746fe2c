#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import math
import time

from std_msgs.msg import Header
from drill_msgs.msg import TowerCtrlStamped, TowerSwitchState, FloatStamped, TowerAngle

# Изначально вертикальный штифт вытянут
vertical_pin_len = 0.5
inclined_pin_len = 0

vertical_pin_max_len = 0.5
inclined_pin_max_len = 0.5

pin_speed = 0.2

move_vert_pin = 0
move_incl_pin = 0

vertical_switch = True
inclined_switch = False

# Изначально наклон 90 градусов (мачта в вертикальном положении)
tilt_speed = 0

tilt = math.pi/2

max_tilt = math.pi/2
min_tilt = 0

time_start = time.time()

time_start_global = time.time()
been_stopped = False

rospy.init_node('tower_sim')

rate = rospy.Rate(100)


def stop_sim(after_sec):
    global been_stopped
    global time_start_global
    if not been_stopped and time.time() - time_start_global >= after_sec:
        been_stopped = True
        raw_input('Press Enter to continue...')


def control_callback(msg):
    global move_vert_pin
    global move_incl_pin
    global tilt_speed

    move_vert_pin = msg.vertical_pin_movement
    move_incl_pin = msg.inclined_pin_movement

    tilt_speed = msg.tilt_speed


rospy.Subscriber('/tower_control', TowerCtrlStamped, control_callback)
tower_state_pub = rospy.Publisher('/tower_state', TowerAngle, queue_size=1)
switch_state_pub = rospy.Publisher('/tower_switch_state', TowerSwitchState)


while not rospy.is_shutdown():
    time_delta = rospy.Time.now().to_sec() - time_start

    vertical_pin_len += move_vert_pin * pin_speed * time_delta
    inclined_pin_len += move_incl_pin * pin_speed * time_delta

    if vertical_pin_len <= 0:
        vertical_pin_len = 0

    if inclined_pin_len <= 0:
        inclined_pin_len = 0

    print("\n\n\nvert_pin_len: {}\nincl_pin_len: {}\n".format(vertical_pin_len, inclined_pin_len))

    if vertical_pin_len >= vertical_pin_max_len:
        vertical_switch = True

    elif inclined_pin_len >= inclined_pin_max_len:
        inclined_switch = True

    else:
        
        vertical_switch = False
        inclined_switch = False

    print('vert_switch_on: {}\nincl_switch_on: {}\n'.format(vertical_switch, inclined_switch))

    if not any((vertical_switch, inclined_switch)):
        tilt += tilt_speed * time_delta

        if tilt > max_tilt:
            tilt = max_tilt
        elif tilt < min_tilt:
            tilt = min_tilt

    print('tilt: {}'.format(tilt))

    state = TowerAngle()
    state.value = tilt
    state.header = Header(stamp=rospy.get_rostime())

    switch_state = TowerSwitchState()
    switch_state.vertical_switch_on = vertical_switch
    switch_state.inclined_switch_on = inclined_switch
    switch_state.header = Header(stamp=rospy.get_rostime())

    # На заданной секунде один раз останавливаем симулятор, чтобы проверить
    # работу ноды с устаревшим стейтом.
    sec = 5
    #stop_sim(after_sec=sec)

    tower_state_pub.publish(state)
    switch_state_pub.publish(switch_state)

    time_start = time.time()

    rate.sleep()
