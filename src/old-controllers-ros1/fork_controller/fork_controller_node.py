#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import json

from state_machine_node import AbstractNodeStateMachine
from fork_controller.models import OpenState, OpeningState, ClosedState, ClosingState, MissedState
from common_msgs.msg import Action
from drill_msgs.msg import VehicleBehaviorMode, FloatStamped, ForkSwitchState

class ForkControlNode(AbstractNodeStateMachine):

    def drill_initialize(self, *args, **kwargs):

        # Set the current state to OpenState
        self.set_current_state(OpenState(self))

        # Add additional states to the state machine
        self.add_states([OpeningState(self), ClosedState(self), ClosingState(self), MissedState(self)])

        # Initialize variables
        self.action = None
        self.new_action_flag = False
        self.do_open = False
        self.fork_ctrl = 0

        # Create a publisher for fork control commands
        self.fork_ctrl_pub = rospy.Publisher(self.global_params['topics']['fork_control'],
                                            FloatStamped, queue_size=10)

        # Check if fork is present based on system flags
        self.fork_present = self.global_params['system_flags']['fork_present']
    def subscribers_initialize(self):
        # Subscribe to fork switch state topic
        self.add_subscriber(self.global_params['topics']['fork_switch_state'], ForkSwitchState,
                            'fork_switch_state')

        # Subscribe to main mode topic
        self.add_subscriber(self.global_params['topics']['main_mode'], VehicleBehaviorMode,
                            'main_mode')

        # Subscribe to fork action topic
        rospy.Subscriber(self.global_params['topics']['fork_action'], Action,
                         self.action_callback)

    def stop_control(self):
        # Stop fork control by setting fork_ctrl to 0
        self.fork_ctrl = 0

    def publish_fork_ctrl(self):
        # Publish fork control command
        message = FloatStamped()
        message.header.stamp = rospy.Time.now()
        message.value = self.fork_ctrl
        self.fork_ctrl_pub.publish(message)

    def action_callback(self, message):
        try:
            # Parse action message data as JSON
            read_json = json.loads(message.data)
            required_keys = (
                'open',
            )
            for k in required_keys:
                if k not in read_json:
                    err_msg = "Action data is incomplete or invalid"
                    self.handle_internal_error(error_message=err_msg)
                    return

            # Check if the requested state is different from the current state
            if (read_json['open'] and self.get_current_state().get_name() != 'OPEN') or \
                    (not read_json['open'] and self.get_current_state().get_name() != 'CLOSED'):
                self.action = message
                self.new_action_flag = True
                self.do_open = read_json['open']
                self._cur_action_seq = message.seq
                self._last_action_seq = message.seq
            else:
                self.log(
                    "Already in requested state, do nothing!",
                    event=self.global_params['events']['already_done']
                )
        except ValueError:
            err_msg = "Action data is not in valid json format"
            self.handle_internal_error(error_message=err_msg)
            return

    def check_data(self):
        if not self.fork_present:
            # If fork is not present, set the state to OPEN
            self.set_state('OPEN')
            return False

        is_error = False

        # Check if the current mode allows operation, otherwise stop operation
        if self.subs.main_mode is None or (self.new_action_flag and self.subs.main_mode.mode not in self.allowed_modes):
            is_error = True

        # Check if the vehicle is in remote mode
        if self.subs.main_mode is not None and (self.subs.main_mode.mode == VehicleBehaviorMode.REMOTE or
                self.subs.main_mode.mode == VehicleBehaviorMode.REMOTE_WAIT or
                self.subs.main_mode.mode == VehicleBehaviorMode.REMOTE_PREPARE):
            is_error = True
        
        return not is_error

    def do_work_after(self):
        # Publish fork control command
        self.publish_fork_ctrl()

    def handle_timeout(self, error_message, event_name):
        err_msg = error_message
        err_type = self.node.global_params['Reports']['Critical']['Action_failed']
        self.node.handle_internal_error(error_message=err_msg,
                                        error_type=err_type,
                                        event=self.node.global_params['events'][event_name])