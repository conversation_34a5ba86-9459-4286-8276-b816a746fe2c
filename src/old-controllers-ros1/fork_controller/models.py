#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy

from .constants import OPENING, OPEN, CLOSING, CLOSED, MISSED
from state_machine_node import AbstractState

class OpeningState(AbstractState):
   def __init__(self, node):
       super(OpeningState, self).__init__(node, OPENING)

   def do_work(self):
       self.node.fork_ctrl = -1
       if rospy.get_time() - max(self.node.get_change_state_timestamp(), self.node.last_empty_loop_ts) > self.node.opening_time:
           self.node.handle_timeout('Could not open fork', 'rc_open_fork')

       # Transition to OPEN state when rear switch is on
       if self.node.subs.fork_switch_state.rear_switch_on:
           self.node.set_state(OPEN)


class OpenState(AbstractState):
   def __init__(self, node):
       super(OpenState, self).__init__(node, OPEN)

   def on_transition_to(self):
       self.node._cur_action_seq = -1

   def do_work(self):
       self.node.stop_control()
       # Restore open state if rear switch is off
       if not self.node.subs.fork_switch_state.rear_switch_on:
           self.node.log("Restore open", loglevel=2)
           self.node.set_state(OPENING)
           return
       if self.node.new_action_flag and not self.node.do_open:
           self.node.set_state(CLOSING)


class ClosingState(AbstractState):
    def __init__(self, node):
        super(ClosingState, self).__init__(node, CLOSING)

    def on_transition_to(self):
        self.movement_started = False
        self.start_time = rospy.get_time()
        self.rear_switch_initial = self.node.subs.fork_switch_state.rear_switch_on
        self.reverse = False

    def do_work(self):
        current_time = rospy.get_time()
        self.node.fork_ctrl = 1 if not self.reverse else -1

        if not self.movement_started:
            # Check if the state of rear_switch_on has changed
            if self.node.subs.fork_switch_state.rear_switch_on != self.rear_switch_initial:
                # Movement has started
                self.movement_started = True
                self.start_time = current_time  # Reset timer for closing
            elif current_time - self.start_time > self.node.node_params['start_timeout']:
                # Movement did not start within the specified time, handle as failure
                self.node.handle_timeout('Fork did not start closing', 'rc_close_fork')
                self.timeout_occurred = True
                self.node.fork_ctrl = 0
                return


        if self.movement_started:
            # Check if the closing time has passed
            if current_time - self.start_time > self.node.closing_time:
                self.reverse = True
                if self.node.subs.fork_switch_state.rear_switch_on:
                    self.node.log("Fork opened after failed closing, transitioning to MISSED state", loglevel=2)
                    self.node.set_state(MISSED)
            elif self.node.subs.fork_switch_state.front_switch_on:
                # Successfully closed
                self.node.set_state(CLOSED)



class ClosedState(AbstractState):
   def __init__(self, node):
       super(ClosedState, self).__init__(node, CLOSED)

   def on_transition_to(self):
       self.node._cur_action_seq = -1

   def do_work(self):
       self.node.stop_control()
       # Restore closed state if front switch is off
       if not self.node.subs.fork_switch_state.front_switch_on:
           self.node.log("Restore closed", loglevel=2)
           self.node.set_state(CLOSING)
           return
       if self.node.new_action_flag and self.node.do_open:
           self.node.set_state(OPENING)


class MissedState(AbstractState):
   def __init__(self, node):
       super(MissedState, self).__init__(node, MISSED)

   def on_transition_to(self):
       self.node._cur_action_seq = -1
       self.node.stop_control()
       self.node.log("Entered MISSED state, waiting for new action", loglevel=2)

   def do_work(self):
       # Wait for new action
       if self.node.new_action_flag:
           if self.node.do_open:
               self.node.log("Received open action in MISSED state", loglevel=2)
               self.node.set_state(OPENING)
           else:
               self.node.log("Received close action in MISSED state", loglevel=2)
               self.node.set_state(CLOSING)
           self.node.new_action_flag = False
