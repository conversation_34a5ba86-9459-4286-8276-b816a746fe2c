#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import time

from drill_msgs.msg import ArmState, FloatStamped

ts = time.time()
time_start_global = time.time()
been_stopped = False

rospy.init_node("arm_sim")

max_len = 0.3
min_len = 0
cur_len = 0

ctrl2speed = 0.1
ctrl = 0
open_switch_on = True
close_switch_on = False

# Частота паблишинга
r = 30
rate = rospy.Rate(r)


def stop_sim(after_sec):
    global been_stopped
    global time_start_global
    if not been_stopped and rospy.Time.now().to_sec() - time_start_global >= after_sec:
        been_stopped = True
        raw_input('Press Enter to continue...')


def ctrl_callback(msg):
    global ctrl
    ctrl = msg.value


state_pub = rospy.Publisher("/arm_switch_state", ArmState, queue_size=1)
rospy.Subscriber("/arm_control", FloatStamped, ctrl_callback)

while not rospy.is_shutdown():
    # Время одной иттерации
    delta_time = rospy.get_rostime().to_sec() - ts
    print('path_len: {}'.format(cur_len))
    print('open_switch_on: {}'.format(open_switch_on))
    print('close_switch_on: {}\n'.format(close_switch_on))

    cur_len += ctrl * ctrl2speed * delta_time
    if cur_len >= max_len:
        cur_len = max_len
        open_switch_on = True
    elif cur_len < max_len:
        open_switch_on = False

    if cur_len > min_len:
        close_switch_on = False
    elif cur_len <= min_len:
        cur_len = min_len
        close_switch_on = True

    # На заданной секунде один раз останавливаем симулятор, чтобы проверить
    # работу ноды с устаревшим стейтом.
    sec = 5
    #stop_sim(after_sec=sec)

    s = ArmState()
    s.header.stamp = rospy.get_rostime()
    s.open_switch_on = open_switch_on
    s.close_switch_on = close_switch_on
    state_pub.publish(s)

    ts = time.time()
    rate.sleep()
