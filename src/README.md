# Drill Onboard Software

[![pipeline status](https://gitlab.com/vistmt/drill_onboard/badges/master/pipeline.svg)](https://gitlab.com/vistmt/drill_onboard/-/commits/master)
[![coverage report](https://gitlab.com/vistmt/drill_onboard/badges/master/coverage.svg)](https://gitlab.com/vistmt/drill_onboard/-/commits/master)
[![ROS Jazzy](https://img.shields.io/badge/ROS-Jazzy-blue)](https://docs.ros.org/en/jazzy/)

ROS 2 Jazzy-based control software for the robotic drilling system.

## Key Components

- **Driller Node**: Main drilling control finite state machine with 11 states (idle, touchdown, drilling, pullup, etc.)
- **Drill Regulator**: Low-level PID controllers for feed speed, rotation speed, and pressure control
- **Safety Manager**: Comprehensive safety system with vibration monitoring, arm management, and stuck detection
- **Base Node Framework**: Reusable ROS2 node architecture with parameter management and logging
- **Modbus Node**: Industrial communication interface for hardware control
- **Parameter Server**: Centralized configuration management with profile support

## Overview

This repository contains the onboard control software for the robotic drilling machine, built using ROS 2 Jazzy. The codebase follows ROS 2 best practices and includes a comprehensive test suite.

## Project Structure

```
drill_onboard/
├── docker/                   # Docker-related files
│   ├── Dockerfile.ci         # CI Docker image
│   └── entrypoint.sh         # Docker entrypoint script
├── scripts/                  # Utility scripts
│   ├── ci_test_local.sh      # Local CI testing
│   └── test_package.sh       # Test single package
├── src/                      # Source packages
│   ├── base_node/            # BaseNode and BaseFSM framework
│   ├── modbus_node/          # Modbus communication node
│   ├── driller/              # Main drilling control FSM
│   ├── drill_regulator/      # Low-level drill control
│   ├── params_server/        # Parameter management server
│   ├── state_tracker/        # State tracking and monitoring
│   ├── drill_msgs/           # Custom ROS2 message definitions
│   ├── common_msgs/          # Common message definitions
│   └── ros-foxglove-bridge/  # Foxglove visualization bridge
├── .gitlab-ci.yml            # GitLab CI configuration
└── .dockerignore             # Files to exclude from Docker
```

## Development

### Prerequisites

- [ROS 2 Jazzy](https://docs.ros.org/en/jazzy/Installation.html)
- [Docker](https://docs.docker.com/get-docker/)
- Git

### Local Testing

You can test the entire workspace or specific packages using the provided scripts.

**Test the entire workspace:**

```bash
./scripts/ci_test_local.sh

# With verbose output
./scripts/ci_test_local.sh --verbose

# Without cleaning previous build artifacts
./scripts/ci_test_local.sh --no-clean
```

**Test a specific package:**

```bash
# Test the modbus_node package
./scripts/test_package.sh modbus_node

# Test with verbose output
./scripts/test_package.sh modbus_node --verbose

# Test without cleaning previous build artifacts
./scripts/test_package.sh modbus_node --no-clean

# Run a specific test
./scripts/test_package.sh modbus_node --test=test_io_configuration

# View all options
./scripts/test_package.sh --help
```

### Running without Docker

If you have ROS 2 Jazzy installed locally, you can build and test directly:

```bash
# Build all packages
colcon build

# Test all packages
colcon test

# Test a specific package
colcon test --packages-select modbus_node
```

## Test Results

Test results are generated in JUnit XML format in the following locations:

- All packages: `build/<package_name>/test_results/<package_name>/`
- Python tests: `build/<package_name>/pytest.xml`

These test results are automatically collected by CI and displayed in the GitLab CI interface.

## CI/CD

This project uses GitLab CI for continuous integration and deployment. The CI pipeline includes:

1. Linting (code style checks)
2. Building (all packages)
3. Testing (unit and integration tests)
4. Test reporting with coverage analysis
5. Deployment (when merging to main branch)

Test reports and code coverage metrics are available in the GitLab CI/CD interface:
- [Pipeline Status](https://gitlab.com/vistmt/drill_onboard/-/pipelines)
- [Test Reports](https://gitlab.com/vistmt/drill_onboard/-/pipelines) (click on any pipeline, then the "Tests" tab)
- [Coverage Reports](https://gitlab.com/vistmt/drill_onboard/-/graphs/master/charts) (under repository Analytics)

## License

Copyright (c) 2025 Zyfra Robotics. All Rights Reserved.  
Proprietary and confidential.
