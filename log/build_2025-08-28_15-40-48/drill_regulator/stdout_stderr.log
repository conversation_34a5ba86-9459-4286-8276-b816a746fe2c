Traceback (most recent call last):
  File "/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/executor/__init__.py", line 91, in __call__
    rc = await self.task(*args, **kwargs)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/__init__.py", line 93, in __call__
    return await task_method(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/build.py", line 145, in build
    temp_symlinks = self._symlinks_in_build(args, setup_py_data)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/build.py", line 299, in _symlinks_in_build
    raise RuntimeError(
RuntimeError: The package_dir contains a mapping from 'drill_regulator' to 'drill_regulator' which is also a key
