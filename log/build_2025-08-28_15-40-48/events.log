[0.000000] (-) TimerEvent: {}
[0.000095] (-) JobUnselected: {'identifier': 'base_node'}
[0.000128] (-) JobUnselected: {'identifier': 'can_decoder'}
[0.000194] (-) JobUnselected: {'identifier': 'can_encoder'}
[0.000228] (-) JobUnselected: {'identifier': 'can_msgs'}
[0.000278] (-) JobUnselected: {'identifier': 'depth_tracker'}
[0.000298] (-) JobUnselected: {'identifier': 'drill_launch_pack'}
[0.000316] (-) JobUnselected: {'identifier': 'drill_msgs'}
[0.000345] (-) JobUnselected: {'identifier': 'driller'}
[0.000360] (-) JobUnselected: {'identifier': 'foxglove_bridge'}
[0.000374] (-) JobUnselected: {'identifier': 'hal_connector'}
[0.000387] (-) JobUnselected: {'identifier': 'launchpack'}
[0.000399] (-) JobUnselected: {'identifier': 'leveler'}
[0.000411] (-) JobUnselected: {'identifier': 'main_state_machine'}
[0.000423] (-) JobUnselected: {'identifier': 'maneuver_builder_cpp'}
[0.000436] (-) JobUnselected: {'identifier': 'modbus_node'}
[0.000448] (-) JobUnselected: {'identifier': 'params_server'}
[0.000459] (-) JobUnselected: {'identifier': 'path_follower'}
[0.000471] (-) JobUnselected: {'identifier': 'pydubins'}
[0.000483] (-) JobUnselected: {'identifier': 'remote_connector'}
[0.000496] (-) JobUnselected: {'identifier': 'rosx_introspection'}
[0.000508] (-) JobUnselected: {'identifier': 'rtk_connector'}
[0.000520] (-) JobUnselected: {'identifier': 'state_tracker'}
[0.000532] (-) JobUnselected: {'identifier': 'tracks_regulator'}
[0.000545] (drill_regulator) JobQueued: {'identifier': 'drill_regulator', 'dependencies': OrderedDict()}
[0.000562] (drill_regulator) JobStarted: {'identifier': 'drill_regulator'}
[0.101385] (-) TimerEvent: {}
[0.206482] (-) TimerEvent: {}
[0.307071] (-) TimerEvent: {}
[0.412865] (-) TimerEvent: {}
[0.515990] (-) TimerEvent: {}
[0.617216] (-) TimerEvent: {}
[0.720694] (-) TimerEvent: {}
[0.822081] (-) TimerEvent: {}
[0.925204] (-) TimerEvent: {}
[1.028615] (-) TimerEvent: {}
[1.128823] (-) TimerEvent: {}
[1.245212] (-) TimerEvent: {}
[1.427255] (-) TimerEvent: {}
[1.465906] (drill_regulator) StderrLine: {'line': b'Traceback (most recent call last):\n  File "/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/executor/__init__.py", line 91, in __call__\n    rc = await self.task(*args, **kwargs)\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/__init__.py", line 93, in __call__\n    return await task_method(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/build.py", line 145, in build\n    temp_symlinks = self._symlinks_in_build(args, setup_py_data)\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/build.py", line 299, in _symlinks_in_build\n    raise RuntimeError(\nRuntimeError: The package_dir contains a mapping from \'drill_regulator\' to \'drill_regulator\' which is also a key\n'}
[1.466617] (drill_regulator) JobEnded: {'identifier': 'drill_regulator', 'rc': 1}
[1.478801] (-) EventReactorShutdown: {}
