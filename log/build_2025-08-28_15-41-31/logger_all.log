[0.239s] DEBUG:colcon:Command line arguments: ['/Users/<USER>/.ros2_venv/bin/colcon', 'build', '--packages-select', 'drill_regulator', '--symlink-install']
[0.239s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['drill_regulator'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x103d6a9d0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x103ef4f90>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x103ef4f90>>)
[0.406s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.406s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.406s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.406s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.406s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.406s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.407s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/Users/<USER>/Work/drill2/onboard'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['ignore', 'ignore_ament_install']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ignore'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ignore_ament_install'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['colcon_pkg']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'colcon_pkg'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['colcon_meta']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'colcon_meta'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extensions ['ros']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/base_node) by extension 'ros'
[0.421s] DEBUG:colcon.colcon_core.package_identification:Package 'src/base_node' with type 'ros.ament_python' and name 'base_node'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['ignore', 'ignore_ament_install']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ignore'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ignore_ament_install'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['colcon_pkg']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'colcon_pkg'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['colcon_meta']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'colcon_meta'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extensions ['ros']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_decoder) by extension 'ros'
[0.422s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_decoder' with type 'ros.ament_python' and name 'can_decoder'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['ignore', 'ignore_ament_install']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ignore'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ignore_ament_install'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['colcon_pkg']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'colcon_pkg'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['colcon_meta']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'colcon_meta'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extensions ['ros']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_encoder) by extension 'ros'
[0.422s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_encoder' with type 'ros.ament_python' and name 'can_encoder'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ignore'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ignore_ament_install'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['colcon_pkg']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'colcon_pkg'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['colcon_meta']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'colcon_meta'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extensions ['ros']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_msgs) by extension 'ros'
[0.423s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_msgs' with type 'ros.ament_cmake' and name 'can_msgs'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['ignore', 'ignore_ament_install']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ignore'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ignore_ament_install'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['colcon_pkg']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'colcon_pkg'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['colcon_meta']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'colcon_meta'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extensions ['ros']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/depth_tracker) by extension 'ros'
[0.424s] DEBUG:colcon.colcon_core.package_identification:Package 'src/depth_tracker' with type 'ros.ament_python' and name 'depth_tracker'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['ignore', 'ignore_ament_install']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ignore'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ignore_ament_install'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['colcon_pkg']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'colcon_pkg'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['colcon_meta']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'colcon_meta'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['ros']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'ros'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['cmake', 'python']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'cmake'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'python'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extensions ['python_setup_py']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/docker) by extension 'python_setup_py'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ignore'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ignore_ament_install'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['colcon_pkg']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'colcon_pkg'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['colcon_meta']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'colcon_meta'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extensions ['ros']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_msgs) by extension 'ros'
[0.425s] DEBUG:colcon.colcon_core.package_identification:Package 'src/drill_msgs' with type 'ros.ament_cmake' and name 'drill_msgs'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['ignore', 'ignore_ament_install']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'ignore'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'ignore_ament_install'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['colcon_pkg']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'colcon_pkg'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['colcon_meta']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'colcon_meta'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['ros']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'ros'
[0.426s] DEBUG:colcon.colcon_core.package_identification:Failed to parse potential ROS package manifest in'src/drill_regulator': Error(s) in package 'src/drill_regulator/package.xml':
The manifest contains invalid XML:
not well-formed (invalid token): line 1, column 16
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['cmake', 'python']
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'cmake'
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'python'
[0.426s] DEBUG:colcon.colcon_core.package_identification:Python package in 'src/drill_regulator' passes arguments to the setup() function which requires a different identification extension than 'python'
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extensions ['python_setup_py']
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/drill_regulator) by extension 'python_setup_py'
[0.886s] DEBUG:colcon.colcon_core.package_identification:Package 'src/drill_regulator' with type 'python' and name 'drill_regulator'
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extensions ['ignore', 'ignore_ament_install']
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'ignore'
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'ignore_ament_install'
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extensions ['colcon_pkg']
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'colcon_pkg'
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extensions ['colcon_meta']
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'colcon_meta'
[0.886s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extensions ['ros']
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller) by extension 'ros'
[0.887s] DEBUG:colcon.colcon_core.package_identification:Package 'src/driller' with type 'ros.ament_python' and name 'driller'
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extensions ['ignore', 'ignore_ament_install']
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'ignore'
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'ignore_ament_install'
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extensions ['colcon_pkg']
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'colcon_pkg'
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extensions ['colcon_meta']
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'colcon_meta'
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extensions ['ros']
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'ros'
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extensions ['cmake', 'python']
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'cmake'
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'python'
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extensions ['python_setup_py']
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1) by extension 'python_setup_py'
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extensions ['ignore', 'ignore_ament_install']
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extension 'ignore'
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extension 'ignore_ament_install'
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extensions ['colcon_pkg']
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extension 'colcon_pkg'
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extensions ['colcon_meta']
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extension 'colcon_meta'
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extensions ['ros']
[0.888s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_launch_pack) by extension 'ros'
[0.889s] DEBUG:colcon.colcon_core.package_identification:Package 'src/driller-old-ros1/drill_launch_pack' with type 'ros.catkin' and name 'drill_launch_pack'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_regulator) by extensions ['ignore', 'ignore_ament_install']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_regulator) by extension 'ignore'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/drill_regulator) ignored
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extensions ['ignore', 'ignore_ament_install']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'ignore'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'ignore_ament_install'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extensions ['colcon_pkg']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'colcon_pkg'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extensions ['colcon_meta']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'colcon_meta'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extensions ['ros']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'ros'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extensions ['cmake', 'python']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'cmake'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'python'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extensions ['python_setup_py']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller) by extension 'python_setup_py'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'ignore'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'ignore_ament_install'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extensions ['colcon_pkg']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'colcon_pkg'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extensions ['colcon_meta']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'colcon_meta'
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extensions ['ros']
[0.889s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'ros'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extensions ['cmake', 'python']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'cmake'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'python'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extensions ['python_setup_py']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driller-old-ros1/driller/__pycache__) by extension 'python_setup_py'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['ignore', 'ignore_ament_install']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'ignore'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'ignore_ament_install'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['colcon_pkg']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'colcon_pkg'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['colcon_meta']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'colcon_meta'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['ros']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'ros'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['cmake', 'python']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'cmake'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'python'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extensions ['python_setup_py']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/driver_example) by extension 'python_setup_py'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['ignore', 'ignore_ament_install']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'ignore'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'ignore_ament_install'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['colcon_pkg']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'colcon_pkg'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['colcon_meta']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'colcon_meta'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['ros']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'ros'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['cmake', 'python']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'cmake'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'python'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extensions ['python_setup_py']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external) by extension 'python_setup_py'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/log) by extensions ['ignore', 'ignore_ament_install']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/log) by extension 'ignore'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/log) ignored
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['ignore', 'ignore_ament_install']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'ignore'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'ignore_ament_install'
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['colcon_pkg']
[0.890s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'colcon_pkg'
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['colcon_meta']
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'colcon_meta'
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extensions ['ros']
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/maneuver-builder-cpp) by extension 'ros'
[0.891s] DEBUG:colcon.colcon_core.package_identification:Package 'src/external/maneuver-builder-cpp' with type 'ros.ament_cmake' and name 'maneuver_builder_cpp'
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['ignore', 'ignore_ament_install']
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'ignore'
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'ignore_ament_install'
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['colcon_pkg']
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'colcon_pkg'
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['colcon_meta']
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'colcon_meta'
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extensions ['ros']
[0.891s] Level 1:colcon.colcon_core.package_identification:_identify(src/external/pydubins) by extension 'ros'
[0.892s] DEBUG:colcon.colcon_core.package_identification:Package 'src/external/pydubins' with type 'ros.ament_python' and name 'pydubins'
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['ignore', 'ignore_ament_install']
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'ignore'
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'ignore_ament_install'
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['colcon_pkg']
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'colcon_pkg'
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['colcon_meta']
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'colcon_meta'
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extensions ['ros']
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(src/hal_connector) by extension 'ros'
[0.893s] DEBUG:colcon.colcon_core.package_identification:Package 'src/hal_connector' with type 'ros.ament_python' and name 'hal_connector'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['ignore', 'ignore_ament_install']
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'ignore'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'ignore_ament_install'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['colcon_pkg']
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'colcon_pkg'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['colcon_meta']
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'colcon_meta'
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extensions ['ros']
[0.893s] Level 1:colcon.colcon_core.package_identification:_identify(src/launchpack) by extension 'ros'
[0.894s] DEBUG:colcon.colcon_core.package_identification:Package 'src/launchpack' with type 'ros.ament_python' and name 'launchpack'
[0.895s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['ignore', 'ignore_ament_install']
[0.895s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'ignore'
[0.895s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'ignore_ament_install'
[0.895s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['colcon_pkg']
[0.895s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'colcon_pkg'
[0.895s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['colcon_meta']
[0.895s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'colcon_meta'
[0.895s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extensions ['ros']
[0.895s] Level 1:colcon.colcon_core.package_identification:_identify(src/leveler) by extension 'ros'
[0.895s] DEBUG:colcon.colcon_core.package_identification:Package 'src/leveler' with type 'ros.ament_python' and name 'leveler'
[0.895s] Level 1:colcon.colcon_core.package_identification:_identify(src/log) by extensions ['ignore', 'ignore_ament_install']
[0.895s] Level 1:colcon.colcon_core.package_identification:_identify(src/log) by extension 'ignore'
[0.895s] Level 1:colcon.colcon_core.package_identification:_identify(src/log) ignored
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['ignore', 'ignore_ament_install']
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'ignore'
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'ignore_ament_install'
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['colcon_pkg']
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'colcon_pkg'
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['colcon_meta']
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'colcon_meta'
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extensions ['ros']
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/main_state_machine) by extension 'ros'
[0.896s] DEBUG:colcon.colcon_core.package_identification:Package 'src/main_state_machine' with type 'ros.ament_python' and name 'main_state_machine'
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['ignore', 'ignore_ament_install']
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'ignore'
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'ignore_ament_install'
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['colcon_pkg']
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'colcon_pkg'
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['colcon_meta']
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'colcon_meta'
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extensions ['ros']
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/modbus_node) by extension 'ros'
[0.897s] DEBUG:colcon.colcon_core.package_identification:Package 'src/modbus_node' with type 'ros.ament_python' and name 'modbus_node'
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['ignore', 'ignore_ament_install']
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'ignore'
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'ignore_ament_install'
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['colcon_pkg']
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'colcon_pkg'
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['colcon_meta']
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'colcon_meta'
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extensions ['ros']
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(src/params_server) by extension 'ros'
[0.898s] DEBUG:colcon.colcon_core.package_identification:Package 'src/params_server' with type 'ros.ament_python' and name 'params_server'
[0.898s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['ignore', 'ignore_ament_install']
[0.898s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'ignore'
[0.898s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'ignore_ament_install'
[0.898s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['colcon_pkg']
[0.898s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'colcon_pkg'
[0.898s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['colcon_meta']
[0.898s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'colcon_meta'
[0.898s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extensions ['ros']
[0.898s] Level 1:colcon.colcon_core.package_identification:_identify(src/path_follower) by extension 'ros'
[0.899s] DEBUG:colcon.colcon_core.package_identification:Package 'src/path_follower' with type 'ros.ament_python' and name 'path_follower'
[0.899s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['ignore', 'ignore_ament_install']
[0.899s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'ignore'
[0.899s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'ignore_ament_install'
[0.899s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['colcon_pkg']
[0.899s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'colcon_pkg'
[0.899s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['colcon_meta']
[0.899s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'colcon_meta'
[0.899s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extensions ['ros']
[0.899s] Level 1:colcon.colcon_core.package_identification:_identify(src/remote_connector) by extension 'ros'
[0.900s] DEBUG:colcon.colcon_core.package_identification:Package 'src/remote_connector' with type 'ros.ament_python' and name 'remote_connector'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['ignore', 'ignore_ament_install']
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'ignore'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'ignore_ament_install'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['colcon_pkg']
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'colcon_pkg'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['colcon_meta']
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'colcon_meta'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extensions ['ros']
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros-foxglove-bridge) by extension 'ros'
[0.905s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ros-foxglove-bridge' with type 'ros.ament_cmake' and name 'foxglove_bridge'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['ignore', 'ignore_ament_install']
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'ignore'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'ignore_ament_install'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['colcon_pkg']
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'colcon_pkg'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['colcon_meta']
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'colcon_meta'
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extensions ['ros']
[0.905s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosx_introspection) by extension 'ros'
[0.917s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rosx_introspection' with type 'ros.ament_cmake' and name 'rosx_introspection'
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['ignore', 'ignore_ament_install']
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'ignore'
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'ignore_ament_install'
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['colcon_pkg']
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'colcon_pkg'
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['colcon_meta']
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'colcon_meta'
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extensions ['ros']
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtk_connector) by extension 'ros'
[0.918s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rtk_connector' with type 'ros.ament_python' and name 'rtk_connector'
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'ignore'
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'ignore_ament_install'
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['colcon_pkg']
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'colcon_pkg'
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['colcon_meta']
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'colcon_meta'
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['ros']
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'ros'
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['cmake', 'python']
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'cmake'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'python'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extensions ['python_setup_py']
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/scripts) by extension 'python_setup_py'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['ignore', 'ignore_ament_install']
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'ignore'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'ignore_ament_install'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['colcon_pkg']
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'colcon_pkg'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['colcon_meta']
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'colcon_meta'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extensions ['ros']
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/state_tracker) by extension 'ros'
[0.919s] DEBUG:colcon.colcon_core.package_identification:Package 'src/state_tracker' with type 'ros.ament_python' and name 'state_tracker'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['ignore', 'ignore_ament_install']
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'ignore'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'ignore_ament_install'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['colcon_pkg']
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'colcon_pkg'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['colcon_meta']
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'colcon_meta'
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extensions ['ros']
[0.919s] Level 1:colcon.colcon_core.package_identification:_identify(src/tracks_regulator) by extension 'ros'
[0.920s] DEBUG:colcon.colcon_core.package_identification:Package 'src/tracks_regulator' with type 'ros.ament_python' and name 'tracks_regulator'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['ignore', 'ignore_ament_install']
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'ignore'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'ignore_ament_install'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['colcon_pkg']
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'colcon_pkg'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['colcon_meta']
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'colcon_meta'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['ros']
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'ros'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['cmake', 'python']
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'cmake'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'python'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extensions ['python_setup_py']
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(src/zenoh) by extension 'python_setup_py'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['ignore', 'ignore_ament_install']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'ignore'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'ignore_ament_install'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['colcon_pkg']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'colcon_pkg'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['colcon_meta']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'colcon_meta'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['ros']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'ros'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['cmake', 'python']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'cmake'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'python'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extensions ['python_setup_py']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds) by extension 'python_setup_py'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/build) by extensions ['ignore', 'ignore_ament_install']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/build) by extension 'ignore'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/build) ignored
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/install) by extensions ['ignore', 'ignore_ament_install']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/install) by extension 'ignore'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/install) ignored
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/log) by extensions ['ignore', 'ignore_ament_install']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/log) by extension 'ignore'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(zenoh-plugin-ros2dds/log) ignored
[0.921s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.921s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.921s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.921s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.921s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'can_msgs' in 'src/can_msgs'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'drill_launch_pack' in 'src/driller-old-ros1/drill_launch_pack'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'drill_msgs' in 'src/drill_msgs'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'launchpack' in 'src/launchpack'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'maneuver_builder_cpp' in 'src/external/maneuver-builder-cpp'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'pydubins' in 'src/external/pydubins'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rosx_introspection' in 'src/rosx_introspection'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'base_node' in 'src/base_node'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'can_decoder' in 'src/can_decoder'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'can_encoder' in 'src/can_encoder'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'foxglove_bridge' in 'src/ros-foxglove-bridge'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'main_state_machine' in 'src/main_state_machine'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'params_server' in 'src/params_server'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'remote_connector' in 'src/remote_connector'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rtk_connector' in 'src/rtk_connector'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'state_tracker' in 'src/state_tracker'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'depth_tracker' in 'src/depth_tracker'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'driller' in 'src/driller'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'hal_connector' in 'src/hal_connector'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'leveler' in 'src/leveler'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'modbus_node' in 'src/modbus_node'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'path_follower' in 'src/path_follower'
[0.967s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'tracks_regulator' in 'src/tracks_regulator'
[0.968s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'cmake_args' from command line to 'None'
[0.968s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'cmake_target' from command line to 'None'
[0.968s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.968s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'cmake_clean_cache' from command line to 'False'
[0.968s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'cmake_clean_first' from command line to 'False'
[0.968s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'cmake_force_configure' from command line to 'False'
[0.968s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'ament_cmake_args' from command line to 'None'
[0.968s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'catkin_cmake_args' from command line to 'None'
[0.968s] Level 5:colcon.colcon_core.verb:set package 'drill_regulator' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.968s] DEBUG:colcon.colcon_core.verb:Building package 'drill_regulator' with the following arguments: {'ament_cmake_args': None, 'build_base': '/Users/<USER>/Work/drill2/onboard/build/drill_regulator', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/Users/<USER>/Work/drill2/onboard/install/drill_regulator', 'merge_install': False, 'path': '/Users/<USER>/Work/drill2/onboard/src/drill_regulator', 'symlink_install': True, 'test_result_base': None}
[0.968s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.969s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.969s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/Users/<USER>/Work/drill2/onboard/src/drill_regulator'
[0.973s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.974s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.974s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.150s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/Users/<USER>/Work/drill2/onboard/build/drill_regulator': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/drill_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/drill_regulator/build --no-deps symlink_data
[3.123s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/Users/<USER>/Work/drill2/onboard/build/drill_regulator' returned '1': PYTHONPATH=/Users/<USER>/Work/drill2/onboard/build/drill_regulator/prefix_override:/Users/<USER>/.ros2_venv/lib/python3.11/site-packages/colcon_core/task/python/colcon_distutils_commands:/Users/<USER>/Work/drill2/onboard/install/drill_regulator/lib/python3.11/site-packages:${PYTHONPATH} SHLVL=2 VIRTUAL_ENV_PROMPT=(.ros2_venv) _=/usr/bin/env /Users/<USER>/.ros2_venv/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /Users/<USER>/Work/drill2/onboard/build/drill_regulator/build --no-deps symlink_data
[3.133s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[3.133s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[3.133s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '1'
[3.133s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[3.137s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.notify2': 'notify2' not found
[3.137s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.notify_send': Not used on non-Linux systems
[3.137s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[3.137s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'terminal_notifier'
[3.274s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[3.275s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.ps1'
[3.277s] INFO:colcon.colcon_core.shell:Creating prefix util module '/Users/<USER>/Work/drill2/onboard/install/_local_setup_util_ps1.py'
[3.280s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.ps1'
[3.347s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.sh'
[3.349s] INFO:colcon.colcon_core.shell:Creating prefix util module '/Users/<USER>/Work/drill2/onboard/install/_local_setup_util_sh.py'
[3.350s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.sh'
[3.379s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.bash'
[3.379s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.bash'
[3.400s] INFO:colcon.colcon_core.shell:Creating prefix script '/Users/<USER>/Work/drill2/onboard/install/local_setup.zsh'
[3.401s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/Users/<USER>/Work/drill2/onboard/install/setup.zsh'
